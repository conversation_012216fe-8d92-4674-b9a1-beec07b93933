<?xml version="1.0"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Common.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <connectionStrings/>
    <system.diagnostics>
        <sources>
            <!-- This section defines the logging configuration for My.Application.Log -->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog"/>
                    <!-- Uncomment the below section to write to the Application Event Log -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information"/>
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
    <userSettings>
        <Common.My.MySettings>
            <setting name="ServerName" serializeAs="String">
                <value/>
            </setting>
            <setting name="DatabaseName" serializeAs="String">
                <value/>
            </setting>
            <setting name="CompanyName" serializeAs="String">
                <value/>
            </setting>
            <setting name="UserEpCode" serializeAs="String">
                <value/>
            </setting>
            <setting name="CompanyName_Arabic" serializeAs="String">
                <value/>
            </setting>
            <setting name="CompanyCode" serializeAs="String">
                <value/>
            </setting>
            <setting name="Sage_ServerName" serializeAs="String">
                <value/>
            </setting>
            <setting name="Sage_DatabaseName" serializeAs="String">
                <value/>
            </setting>
            <setting name="UserCanSeeAllDept" serializeAs="String">
                <value>False</value>
            </setting>
        </Common.My.MySettings>
    </userSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup></configuration>
