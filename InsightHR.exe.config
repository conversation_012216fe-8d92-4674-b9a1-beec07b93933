<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="InsightHR.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings />
    <system.diagnostics>
        <sources>
            <!-- This section defines the logging configuration for My.Application.Log -->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog" />
                    <!-- Uncomment the below section to write to the Application Event Log -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information" />
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter" />
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
    <userSettings>
        <InsightHR.My.MySettings>
            <!-- إعدادات كار1 (الافتراضية) -->
            <setting name="ServerName" serializeAs="String">
                <value>192.168.10.60\SQLHR</value>
            </setting>
            <setting name="DatabaseName" serializeAs="String">
                <value>InsightHR</value>
            </setting>
            <setting name="CompanyName" serializeAs="String">
                <value>Kar Company</value>
            </setting>
            <setting name="UserId" serializeAs="String">
                <value>1</value>
            </setting>
            <setting name="UserEpCode" serializeAs="String">
                <value />
            </setting>
            <setting name="CompanyName_Arabic" serializeAs="String">
                <value>شركـــة كـــار</value>
            </setting>
            <setting name="Right2Left" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="CompanyCode" serializeAs="String">
                <value>KAR</value>
            </setting>

            <!-- إعدادات كار2 -->
            <setting name="Kar2_ServerName" serializeAs="String">
                <value>192.168.10.60\SQLHR</value>
            </setting>
            <setting name="Kar2_DatabaseName" serializeAs="String">
                <value>InsightHR_Kar2</value>
            </setting>
            <setting name="Kar2_CompanyName" serializeAs="String">
                <value>Kar2 Company</value>
            </setting>
            <setting name="Kar2_CompanyName_Arabic" serializeAs="String">
                <value>شركـــة كـــار2</value>
            </setting>
            <setting name="Kar2_CompanyCode" serializeAs="String">
                <value>KAR2</value>
            </setting>

            <!-- إعداد الشركة المختارة حالياً -->
            <setting name="SelectedCompany" serializeAs="String">
                <value>KAR</value>
            </setting>
            <setting name="Sage_ServerName" serializeAs="String">
                <value>192.168.10.60\SQLHR</value>
            </setting>
            <setting name="Sage_DatabaseName" serializeAs="String">
                <value>DB</value>
            </setting>
            <setting name="Sage_CommonDatabaseName" serializeAs="String">
                <value>EvolutionCommon</value>
            </setting>
            <setting name="UserCanSeeAllDept" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ExcelPath" serializeAs="String">
                <value>D:\</value>
            </setting>
        </InsightHR.My.MySettings>
    </userSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" /></startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.VisualBasic.PowerPacks.Vs" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
