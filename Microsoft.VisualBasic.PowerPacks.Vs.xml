﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.VisualBasic.PowerPacks.Vs</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualBasic.PowerPacks.BackStyle">
      <summary>Specifies the background transparency for an <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> or <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> control.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.BackStyle.Transparent">
      <summary>A transparent background. This is the default.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.BackStyle.Opaque">
      <summary>A background specified by the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackColor" /> property.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeater">
      <summary>Displays data in a customizable list format.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.AddNew">
      <summary>Adds a new <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> to the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <exception cref="T:System.Data.ReadOnlyException">The <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToAddItems" /> property is set to False.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToAddItems">
      <summary>Gets or sets a value that determines whether users can add a new row to a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> at run time.</summary>
      <returns>true if the user can add rows; otherwise false. The default is true.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToAddItemsChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToAddItems" /> property changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToDeleteItems">
      <summary>Gets or sets a value that determines whether users can delete a row from a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> at run time.</summary>
      <returns>true if the user can delete rows; otherwise false. The default is true.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToDeleteItemsChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToDeleteItems" /> property changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AutoScroll"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AutoScrollMargin"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.AutoScrollMinSize"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.BeginResetItemTemplate">
      <summary>Begins a code block that enables you to reset the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> for a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.CancelEdit">
      <summary>Allows users to cancel an edit to the current child control in the current <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.CreateAccessibilityInstance">
      <summary>Creates a new accessibility object for a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>A new accessibility object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.CreateControlsInstance">
      <summary>This member overrides the <see cref="M:System.Windows.Forms.Control.CreateControlsInstance" /> method.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.CreateParams">
      <summary>This member overrides <see cref="P:System.Windows.Forms.Control.CreateParams" />.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.CurrentItem">
      <summary>Gets the current <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>The currently selected object.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.CurrentItemIndex">
      <summary>Gets or sets the current <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>The index of the current <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.CurrentItemIndexChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.CurrentItemIndex" /> changes.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataError">
      <summary>Occurs when an external data-parsing or validation operation throws an exception, or when an attempt to commit data to a data source fails.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataMember">
      <summary>Gets or sets the name of the list or table in the data source for which the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> is displaying data.</summary>
      <returns>The name of the table or list in the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataSource" /> for which the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> is displaying data. The default is Empty.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataMemberChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataMember" /> property changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataSource">
      <summary>Gets or sets the data source for which the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> is displaying data.</summary>
      <returns>The object that contains data for the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> to display.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataSourceChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataSource" /> property is changed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DefaultPadding">
      <summary>This member overrides the <see cref="P:System.Windows.Forms.Control.DefaultPadding" /> property.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DeletingItems">
      <summary>Occurs when a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> is being deleted.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.DisplayedItemCount(System.Boolean)">
      <summary>Gets the number of <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> items that are visible in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control, optionally including partially displayed items.</summary>
      <returns>The count of displayed items.</returns>
      <param name="includePartialItems">true to include partially displayed items in the count; false to include only fully displayed items.</param>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DrawItem">
      <summary>Occurs when a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> must be drawn.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.EndResetItemTemplate">
      <summary>Ends a code block that enables you to reset the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> for a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.FirstDisplayedItemIndex">
      <summary>Gets the index of the first currently displayed <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>The index of the first displayed <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.IsCurrentItemDirty">
      <summary>Gets a value that determines whether the data for a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control has been changed by a user.</summary>
      <returns>true if the data has been changed; otherwise, false.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCloned">
      <summary>Occurs after the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> and its controls are cloned from the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" />.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCloning">
      <summary>Occurs before the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> and its controls are cloned from the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" />.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCount">
      <summary>Gets or sets the number of <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> items that are in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>The count.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemHeaderSize">
      <summary>Gets or sets the size of the item header in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>The size of the item header, in pixels. The default value is 15.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemHeaderVisible">
      <summary>Gets or sets a value that determines whether item headers are displayed in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>true if the item header will be displayed; otherwise, false. The default is true.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemHeaderVisibleChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemHeaderVisible" /> property changes.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsAdded">
      <summary>Occurs when a new <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> is added to a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsRemoved">
      <summary>Occurs when a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> is deleted from a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate">
      <summary>Gets a template that represents the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> for a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>An object that determines the layout and appearance of items in the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualMode" /> property is set to True and a new value for a child control of a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> is needed.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualMode" /> property is set to True and the value of a child control in the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.LayoutStyle">
      <summary>Gets or sets a value that determines whether a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control will be displayed with a vertical or horizontal orientation.</summary>
      <returns>One of the enumeration values that specifies the layout.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.LayoutStyleChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.LayoutStyle" /> property value changes.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.NewItemNeeded">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualMode" /> property is set to True and the user creates a new blank <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnAllowUserToAddItemsChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToAddItemsChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnAllowUserToDeleteItemsChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.AllowUserToDeleteItemsChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnClientSizeChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Control.ClientSizeChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnCreateControl">
      <summary>Raises the <see cref="M:System.Windows.Forms.Control.CreateControl" /> method.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnCurrentItemIndexChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.CurrentItemIndexChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnDataError(Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataError" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnDataMemberChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataMemberChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnDataSourceChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataSourceChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnDeletingItems(Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DeletingItems" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnDrawItem(Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DrawItem" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemCloned(Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCloned" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemCloning(Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCloning" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemHeaderVisibleChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemHeaderVisibleChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemsAdded(Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsAdded" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemsRemoved(Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsRemoved" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemValueNeeded(Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnItemValuePushed(Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Panel.KeyDown" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnLayoutStyleChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.LayoutStyleChanged" /> event</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Control.MouseDown" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Control.MouseWheel" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnNewItemNeeded(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.NewItemNeeded" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnPaddingChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Control.PaddingChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnRightToLeftChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.Control.RightToLeftChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnScroll(System.Windows.Forms.ScrollEventArgs)">
      <summary>Raises the <see cref="E:System.Windows.Forms.ScrollableControl.Scroll" /> event.</summary>
      <param name="se">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnSelectionColorChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.SelectionColorChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnUserAddedItems(Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserAddedItems" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnUserDeletedItems(Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletedItems" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnUserDeletingItems(Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletingItems" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnValidating(System.ComponentModel.CancelEventArgs)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.OnVirtualModeChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualModeChanged" /> event.</summary>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.ProcessTabKey(System.Boolean)">
      <summary>This member overrides the <see cref="M:System.Windows.Forms.ContainerControl.ProcessTabKey(System.Boolean)" /> method.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.RemoveAt(System.Int32)">
      <summary>Removes a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> at the specified position from a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <param name="index">The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for <paramref name="index" /> is less than 0 or greater than <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCount" /> - 1.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.ScrollControlIntoView(System.Windows.Forms.Control)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.ScrollItemIntoView(System.Int32)">
      <summary>Scrolls a specified <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> into view in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <param name="index">The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for <paramref name="index" /> is less than 0 or greater than <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCount" /> - 1.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.ScrollItemIntoView(System.Int32,System.Boolean)">
      <summary>Scrolls a specified <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> into view in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control, optionally aligning it with the top of the control.</summary>
      <param name="index">The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</param>
      <param name="alignWithTop">true to align the top of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> with the top of the control; otherwise, false.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The value specified for <paramref name="index" /> is less than 0 or greater than <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCount" /> - 1.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.ScrollToControl(System.Windows.Forms.Control)">
      <summary>This member overrides the <see cref="M:System.Windows.Forms.ScrollableControl.ScrollToControl(System.Windows.Forms.Control)" /> method.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.Select(System.Boolean,System.Boolean)">
      <summary>This member overrides the <see cref="M:System.Windows.Forms.Control.Select(System.Boolean,System.Boolean)" /> method.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.SelectionColor">
      <summary>Gets or sets the color that is displayed in the item header of a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control when an item is first selected.</summary>
      <returns>The color to use. The default is <see cref="P:System.Drawing.SystemColors.Highlight" />.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.SelectionColorChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.SelectionColor" /> property is changed.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserAddedItems">
      <summary>Occurs when the user adds a new <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> by pressing the CTRL+N keyboard shortcut.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletedItems">
      <summary>Occurs after the user deletes a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> by pressing the DELETE key.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletingItems">
      <summary>Occurs when the user deletes a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> by pressing the DELETE key.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualMode">
      <summary>Gets or sets a value that indicates whether you have provided your own data-management operations for the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control. </summary>
      <returns>true if the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> uses data-management operations that you provide; otherwise, false. The default is false.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualModeChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.VirtualMode" /> property is changed.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeater.WndProc(System.Windows.Forms.Message@)">
      <summary>This member overrides the <see cref="M:System.Windows.Forms.Control.WndProc(System.Windows.Forms.Message@)" /> method.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DeletingItems" /> and <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletingItems" /> events.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs" /> class.</summary>
      <param name="index">The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that is being deleted.</param>
      <param name="count">The number of items being deleted.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs.ItemCount">
      <summary>Gets the number of items being deleted.</summary>
      <returns>A count of the items.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventArgs.ItemIndex">
      <summary>Gets the index of the item that is being deleted.</summary>
      <returns>The index of the item.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsCancelEventHandler">
      <summary>Represents the method that handles the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DeletingItems" /> and <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletingItems" /> events.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsAdded" />, <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsRemoved" />, <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserAddedItems" />, and <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletedItems" /> events.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs" /> class.</summary>
      <param name="index">The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that is being added or deleted.</param>
      <param name="count">The number of items being added or deleted.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs.ItemCount">
      <summary>Gets the number of items being added to or deleted from a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>A count of the items.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventArgs.ItemIndex">
      <summary>Gets the index of the item being added or deleted.</summary>
      <returns>The index of the item.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterAddRemoveItemsEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsAdded" />, <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemsRemoved" />, <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserAddedItems" />, and <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.UserDeletedItems" /> events.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataError" /> event.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs.#ctor(Microsoft.VisualBasic.PowerPacks.DataRepeaterItem,System.Windows.Forms.Control,System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs" /> class.</summary>
      <param name="item">The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> on which the error occurred.</param>
      <param name="ctl">The control on the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> on which the error occurred.</param>
      <param name="prop">The property on which the error occurred. For most controls, this will be the <see cref="P:System.Windows.Forms.Control.Text" /> property.</param>
      <param name="ex">The exception object.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs.Control">
      <summary>Gets the <see cref="T:System.Windows.Forms.Control" /> that raised the data error.</summary>
      <returns>The control that raised the error.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs.DataRepeaterItem">
      <summary>Gets the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that raised the data error.</summary>
      <returns>The item that contains the control in which the error occurred.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs.Exception">
      <summary>Gets the <see cref="T:System.Exception" /> that represents the error.</summary>
      <returns>The exception object that represents the error.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs.PropertyName">
      <summary>Gets the name of the property of the control that raised the error.</summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventArgs.ThrowException">
      <summary>Gets or sets a value that indicates whether to throw an exception after code execution exits the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataError" /> event handler.</summary>
      <returns>true if the exception is thrown; otherwise, false. The default is false.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterDataErrorEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DataError" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem">
      <summary>Used by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.AccessibilityObject">
      <summary>Gets the <see cref="T:System.Windows.Forms.AccessibleObject" /> that is assigned to the control.</summary>
      <returns>The <see cref="T:System.Windows.Forms.AccessibleObject" /> that is assigned to the control.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.AccessibleDefaultActionDescription">
      <summary>Gets or sets the default action description of the control for use by accessibility client applications.</summary>
      <returns>The default action description of the control for use by accessibility client applications.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.AccessibleDescription">
      <summary>Gets or sets the description of the control that is used by accessibility client applications.</summary>
      <returns>A string that contains the description of the control that is used by accessibility client applications. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.AccessibleName">
      <summary>Gets or sets the name of the control that is used by accessibility client applications.</summary>
      <returns>A string that represents the name of the control that is used by accessibility client applications. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.AccessibleRole">
      <summary>Gets or sets the accessible role of the control.</summary>
      <returns>One of the values of <see cref="T:System.Windows.Forms.AccessibleRole" />. The default is Default.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value assigned is not one of the <see cref="T:System.Windows.Forms.AccessibleRole" /> values.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.Anchor">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>An <see cref="T:System.Windows.Forms.AnchorStyles" /> enumeration.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.CreateAccessibilityInstance">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>An <see cref="T:System.Windows.Forms.AccessibleObject" /> for the Accessibility instance.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.Dock">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>A <see cref="T:System.Windows.Forms.DockStyle" /> enumeration.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.IsCurrent">
      <summary>Gets a value that determines whether a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> is the currently selected item in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>true if the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> is the currently selected item; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.IsDirty">
      <summary>Gets a value that determines whether the data for a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control has been changed by a user.</summary>
      <returns>true if the data has been changed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.ItemIndex">
      <summary>Gets the index of a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
      <returns>The index of the current <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.Location">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>A <see cref="T:System.Drawing.Point" /> that represents the location.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.Name">
      <summary>Gets or sets the name of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object.</summary>
      <returns>The name of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object. The default is an empty string ("").</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.OnEnabledChanged(System.EventArgs)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.OnGotFocus(System.EventArgs)">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <param name="e">The <see cref="T:System.EventArgs" /> for the event.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.OnLostFocus(System.EventArgs)">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <param name="e">The <see cref="T:System.EventArgs" /> for the event.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.OnPreviewKeyDown(System.Windows.Forms.PreviewKeyDownEventArgs)">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <param name="e">The <see cref="T:System.Windows.Forms.PreviewKeyDownEventArgs" /> for the event.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.SetBoundsCore(System.Int32,System.Int32,System.Int32,System.Int32,System.Windows.Forms.BoundsSpecified)">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <param name="x">The new <see cref="P:System.Windows.Forms.Control.Left" /> property value of the control.</param>
      <param name="y">The new <see cref="P:System.Windows.Forms.Control.Top" /> property value of the control.</param>
      <param name="width">The new <see cref="P:System.Windows.Forms.Control.Width" /> property value of the control.</param>
      <param name="height">The new <see cref="P:System.Windows.Forms.Control.Height" /> property value of the control.</param>
      <param name="specified">A bitwise combination of the <see cref="T:System.Windows.Forms.BoundsSpecified" /> values. </param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.TabIndex">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>An Integer that represents the index.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.TabStop">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>true if the control participates in the tab order; otherwise false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.Visible">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <returns>true if the item is visible; otherwise false.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem.WndProc(System.Windows.Forms.Message@)">
      <summary>The <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> object is used internally by the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control to display data at run time. The settings of the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> determine how the data is displayed.</summary>
      <param name="m">The <see cref="T:System.Windows.Forms.Message" /> for the process.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCloning" /> event.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs.#ctor(Microsoft.VisualBasic.PowerPacks.DataRepeaterItem)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs" /> class.</summary>
      <param name="src">The <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> from which the new item will be cloned.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs.Handled">
      <summary>Gets or sets a value that specifies whether the cloning is handled by the developer.</summary>
      <returns>true if you are handling the cloning yourself; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs.Source">
      <summary>Gets the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> from which the new <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> will be cloned.</summary>
      <returns>The object from which the new object will be cloned.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventArgs.Target">
      <summary>Gets or sets the new <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that is the result of the cloning operation.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemCloneEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemCloning" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DrawItem" /> event.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventArgs.#ctor(Microsoft.VisualBasic.PowerPacks.DataRepeaterItem)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventArgs" /> class.</summary>
      <param name="item">The item to be drawn.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventArgs.DataRepeaterItem">
      <summary>Gets a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that provides the data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DrawItem" /> event of a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control</summary>
      <returns>An item that contains the data, which is based on the <see cref="P:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemTemplate" /> property of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.DrawItem" /> event.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> and <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> events.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs.#ctor(System.Int32,System.Windows.Forms.Control,System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs" /> class.</summary>
      <param name="itemIndex">The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that contains the control that is raising the event.</param>
      <param name="ctl">The child control that is raising the event.</param>
      <param name="propertyName">The property of the control that has changed or that needs data.</param>
      <param name="value">The property value that has changed.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs.Control">
      <summary>Gets the child control of a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that is raising the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> or <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> event.</summary>
      <returns>The control that is raising the event.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs.ItemIndex">
      <summary>Gets the index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" /> that contains the control that is raising the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> or <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> event.</summary>
      <returns>The index of the <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItem" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs.PropertyName">
      <summary>Gets the name of the data-bound property of the control that is raising a <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> or <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> event.</summary>
      <returns>The name of the property.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventArgs.Value">
      <summary>Gets the property value that has changed to raise a <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> or <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> event.</summary>
      <returns>The value to assign to the property or save to a data store.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterItemValueEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValueNeeded" /> and <see cref="E:Microsoft.VisualBasic.PowerPacks.DataRepeater.ItemValuePushed" /> events.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An object that contains the event data.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.DataRepeaterLayoutStyles">
      <summary>Provides an enumeration for specifying the orientation of items in a <see cref="T:Microsoft.VisualBasic.PowerPacks.DataRepeater" /> control.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.DataRepeaterLayoutStyles.Vertical">
      <summary>Default. Items will be displayed in a vertical format. A vertical scroll bar will be displayed as necessary.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.DataRepeaterLayoutStyles.Horizontal">
      <summary>Items will be displayed in a horizontal format. A horizontal scroll bar will be displayed as necessary.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.FillGradientStyle">
      <summary>Specifies the fill gradient style for an <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> or <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> control.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillGradientStyle.BackwardDiagonal">
      <summary>A fill gradient in which the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> on the upper-right transitions to the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientColor" /> on the lower-left.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillGradientStyle.Central">
      <summary>A fill gradient in which the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> on the outer edges of the shape transitions to the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientColor" /> in the center.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillGradientStyle.ForwardDiagonal">
      <summary>A fill gradient in which the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> on the upper-left transitions to the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientColor" /> on the lower-right.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillGradientStyle.Horizontal">
      <summary>A fill gradient in which the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> on the left transitions to the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientColor" /> on the right.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillGradientStyle.None">
      <summary>No fill gradient. The <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> is displayed. This is the default.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillGradientStyle.Vertical">
      <summary>A fill gradient in which the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> on the top transitions to the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientColor" /> on the bottom.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.FillStyle">
      <summary>Specifies the fill gradient style for an <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> or <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> control.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.BackwardDiagonal">
      <summary>A pattern of lines that slant from upper right to lower left.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Cross">
      <summary>A pattern of horizontal and vertical lines that cross.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DarkDownwardDiagonal">
      <summary>A pattern of diagonal lines that slant to the right from top points to bottom points, are spaced 50 percent closer together than <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.ForwardDiagonal" /> lines, and are double their widths. This pattern is not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DarkHorizontal">
      <summary>A pattern of horizontal lines that are spaced 50 percent closer together than <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Horizontal" /> lines and are double their widths.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DarkUpwardDiagonal">
      <summary>A pattern of diagonal lines that slant to the left from top points to bottom points, are spaced 50 percent closer together than <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.BackwardDiagonal" /> lines, and are double their widths. This pattern is not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DarkVertical">
      <summary>A pattern of vertical lines that are spaced 50 percent closer together than <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Vertical" /> lines and are double their widths.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DashedDownwardDiagonal">
      <summary>A pattern of dashed diagonal lines that slant to the right from top points to bottom points.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DashedHorizontal">
      <summary>A pattern of dashed horizontal lines.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DashedUpwardDiagonal">
      <summary>A pattern of dashed diagonal lines that slant to the left from top points to bottom points.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DashedVertical">
      <summary>A pattern of dashed vertical lines.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DiagonalBrick">
      <summary>A pattern that has the appearance of layered bricks that slant to the left from top points to bottom points.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DiagonalCross">
      <summary>A pattern of crossing diagonal lines.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Divot">
      <summary>A pattern that has the appearance of divots.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DottedDiamond">
      <summary>A pattern of forward crossing diagonal lines, each of which is composed of dots that cross.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.DottedGrid">
      <summary>A pattern of horizontal and vertical lines, each of which is composed of dots that cross.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.ForwardDiagonal">
      <summary>A pattern of lines that slant from upper left to lower right.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Horizontal">
      <summary>A pattern of horizontal lines.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.HorizontalBrick">
      <summary>A pattern that has the appearance of horizontally layered bricks.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LargeCheckerBoard">
      <summary>A pattern that has the appearance of a checkerboard, with squares that are double the size of the HatchStyle.SmallCheckerBoard squares.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LargeConfetti">
      <summary>A pattern that has the appearance of confetti and is composed of larger pieces than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.SmallConfetti" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LightDownwardDiagonal">
      <summary>A pattern of diagonal lines that slant to the right from top points to bottom points and are spaced 50 percent farther apart than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.BackwardDiagonal" />. This pattern is not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LightHorizontal">
      <summary>A pattern of horizontal lines that are spaced 50 percent farther apart than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Horizontal" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LightUpwardDiagonal">
      <summary>A pattern of diagonal lines that slant to the left from top points to bottom points and are spaced 50 percent farther apart than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.BackwardDiagonal" />. This pattern is not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LightVertical">
      <summary>A pattern of vertical lines that are spaced 50 percent farther apart than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Vertical" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.NarrowHorizontal">
      <summary>A pattern of horizontal lines that are spaced 75 percent closer together than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Horizontal" /> (or 25 percent closer together than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LightHorizontal" />).</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.NarrowVertical">
      <summary>A pattern of vertical lines that are spaced 75 percent closer together than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Vertical" /> (or 25 percent closer together than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.LightVertical" />).</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.OutlineShapedDiamond">
      <summary>A pattern of crossing diagonal lines that that are not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent05">
      <summary>A 5-percent dot pattern. The ratio of foreground color to background color is 5:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent10">
      <summary>A 10-percent dot pattern. The ratio of foreground color to background color is 10:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent20">
      <summary>A 20-percent dot pattern. The ratio of foreground color to background color is 20:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent25">
      <summary>A 25-percent dot pattern. The ratio of foreground color to background color is 25:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent30">
      <summary>A 30-percent dot pattern. The ratio of foreground color to background color is 30:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent40">
      <summary>A 40-percent dot pattern. The ratio of foreground color to background color is 40:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent50">
      <summary>A 50-percent dot pattern. The ratio of foreground color to background color is 50:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent60">
      <summary>A 60-percent dot pattern. The ratio of foreground color to background color is 60:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent70">
      <summary>A 70-percent dot pattern. The ratio of foreground color to background color is 70:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent75">
      <summary>A 75-percent dot pattern. The ratio of foreground color to background color is 75:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent80">
      <summary>A 80-percent dot pattern. The ratio of foreground color to background color is 80:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Percent90">
      <summary>A 90-percent dot pattern. The ratio of foreground color to background color is 90:100.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Plaid">
      <summary>A plaid pattern.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Shingle">
      <summary>A pattern that has the appearance of diagonally layered shingles that slant to the right from top points to bottom points.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.SmallCheckerBoard">
      <summary>A pattern that has the appearance of a checkerboard.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.SmallConfetti">
      <summary>A pattern that has the appearance of confetti.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.SmallGrid">
      <summary> A pattern of horizontal and vertical lines that cross and are spaced 50 percent closer together than those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Cross" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Solid">
      <summary>An opaque background in the color specified by the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor" /> property.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.SolidDiamond">
      <summary>A pattern that has the appearance of a checkerboard positioned diagonally.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Sphere">
      <summary>A pattern that has the appearance of spheres laid adjacent to each other.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Transparent">
      <summary>No pattern. If the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackStyle" /> property is set to <see cref="F:Microsoft.VisualBasic.PowerPacks.BackStyle.Opaque" />, the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackColor" /> will be displayed.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Trellis">
      <summary>A pattern that has the appearance of a trellis.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Vertical">
      <summary>A pattern of vertical lines.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Wave">
      <summary>A pattern of horizontal lines that are composed of tildes (~).</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.Weave">
      <summary>A pattern that has the appearance of a woven material.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.WideDownwardDiagonal">
      <summary>A pattern of diagonal lines that slant to the right from top points to bottom points, have the same spacing as those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.ForwardDiagonal" />, and are triple their width. This pattern is not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.WideUpwardDiagonal">
      <summary>A pattern of diagonal lines that slant to the left from top points to bottom points, have the same spacing as those of <see cref="F:Microsoft.VisualBasic.PowerPacks.FillStyle.BackwardDiagonal" />, and are triple their width. This pattern is not antialiased.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.FillStyle.ZigZag">
      <summary>A pattern of horizontal lines that are composed of zigzags.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.LineShape">
      <summary>Represents a control displayed as a horizontal, vertical, or diagonal line.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.#ctor(Microsoft.VisualBasic.PowerPacks.ShapeContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> class, specifying the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> where it will be parented.</summary>
      <param name="parent">A <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> where the shape will be parented</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> class, specifying the coordinates for the line.</summary>
      <param name="x1">The X (horizontal) coordinate of the starting point of the line.</param>
      <param name="y1">The Y (vertical) coordinate of the starting point of the line.</param>
      <param name="x2">The X (horizontal) coordinate of the ending point of the line.</param>
      <param name="y2">The Y (vertical) coordinate of the ending point of the line.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.CreateAccessibilityInstance">
      <summary>Creates an object to provide information that accessibility applications use to adjust an application's user interface (UI) for users who have disabilities.</summary>
      <returns>A <see cref="T:System.Windows.Forms.AccessibleObject" /> class.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.DrawToBitmap(System.Drawing.Bitmap,System.Drawing.Rectangle)">
      <summary>Supports drawing to the specified bitmap.</summary>
      <param name="bitmap">The <see cref="T:System.Drawing.Bitmap" /> to be drawn to.</param>
      <param name="targetBounds">The <see cref="T:System.Drawing.Rectangle" /> within which the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> is drawn.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.LineShape.EndPoint">
      <summary>Gets or sets the ending coordinates of a line drawn by a <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control.</summary>
      <returns>A <see cref="T:System.Drawing.Point" /> structure that represents the ending coordinates of the line.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.LineShape.EndPointChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.LineShape.EndPoint" /> property value changes.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.HitTest(System.Int32,System.Int32)">
      <summary>Gets information about the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control at the specified point on the screen.</summary>
      <returns>true if the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control is located at the specified coordinates; otherwise false.</returns>
      <param name="x">The horizontal screen coordinate.</param>
      <param name="y">The vertical screen coordinate.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.OnEndPointChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.LineShape.EndPointChanged" /> event.</summary>
      <param name="e">A <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Paint" /> event.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.PaintEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.OnStartPointChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.LineShape.StartPointChanged" /> event.</summary>
      <param name="e">A <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.LineShape.Scale(System.Drawing.SizeF)">
      <summary>Resizes the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control by the specified scaling factor.</summary>
      <param name="factor">A <see cref="T:System.Drawing.SizeF" /> structure that contains the X (horizontal) and Y (vertical) scaling factors.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.LineShape.StartPoint">
      <summary>Gets or sets the starting coordinates of a line drawn by a <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control.</summary>
      <returns>A <see cref="T:System.Drawing.Point" /> structure that represents the starting coordinates of the line.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.LineShape.StartPointChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.LineShape.StartPoint" /> property value changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.LineShape.X1">
      <summary>Gets or sets the X coordinate of the starting point of a line drawn by a <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control.</summary>
      <returns>An Integer that represents the X (horizontal) coordinate of the starting point of the line.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.LineShape.X2">
      <summary>Gets or sets the X coordinate of the ending point of a line drawn by a <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control.</summary>
      <returns>An Integer that represents the X (horizontal) coordinate of the ending point of the line.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.LineShape.Y1">
      <summary>Gets or sets the Y coordinate of the starting point of a line drawn by a <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control.</summary>
      <returns>An Integer that represents the Y (vertical) coordinate of the starting point of the line.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.LineShape.Y2">
      <summary>Gets or sets the Y coordinate of the ending point of a line drawn by a <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> control.</summary>
      <returns>An Integer that represents the Y (vertical) coordinate of the ending point of the line.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.OvalShape">
      <summary>Represents a control displayed as a circle or oval.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.OvalShape.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.OvalShape.#ctor(Microsoft.VisualBasic.PowerPacks.ShapeContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> class, specifying the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> in which it will be contained.</summary>
      <param name="parent">A <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> in which the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> will be contained.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.OvalShape.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> class, specifying its location and size.</summary>
      <param name="x">An Integer representing the left edge (in pixels)of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" />.</param>
      <param name="y">An Integer representing the top edge (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" />.</param>
      <param name="width">An Integer representing the width (in pixels)of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" />.</param>
      <param name="height">An Integer representing the height (in pixels)of the <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" />.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.RectangleShape">
      <summary>Represents a control displayed as a square, rectangle, rounded square, or rounded rectangle.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.RectangleShape.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.RectangleShape.#ctor(Microsoft.VisualBasic.PowerPacks.ShapeContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> class, specifying the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> that will contain it.</summary>
      <param name="parent">A <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> that will contain the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" />.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.RectangleShape.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> class, specifying its location and size.</summary>
      <param name="x">An Integer representing the left edge (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" />.</param>
      <param name="y">An Integer representing the top edge (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" />.</param>
      <param name="width">An Integer representing the width (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" />.</param>
      <param name="height">An Integer representing the height (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" />.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.RectangleShape.CornerRadius">
      <summary>Gets or sets the radius for the corners of rounded rectangle and rounded square shapes.</summary>
      <returns>An Integer representing the radius. The default is 0, or no radius.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Shape">
      <summary>Implements the basic functionality common to line and shape controls.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.#ctor(Microsoft.VisualBasic.PowerPacks.ShapeContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> class.</summary>
      <param name="parent">The <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />  to be the parent of the shape.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.AccessibilityObject">
      <summary>Gets the <see cref="T:System.Windows.Forms.AccessibleObject" /> that is assigned to the control.</summary>
      <returns>The <see cref="T:System.Windows.Forms.AccessibleObject" /> that is assigned to the control.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.AccessibleDefaultActionDescription">
      <summary>Gets or sets the default action description of the control for use by accessibility client applications.</summary>
      <returns>The default action description of the control for use by accessibility client applications. For <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" />, the default action is <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Click" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.AccessibleDescription">
      <summary>Gets or sets the description of the control that is used by accessibility client applications.</summary>
      <returns>A <see cref="T:System.String" /> that contains the description of the control that is used by accessibility client applications. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.AccessibleName">
      <summary>Gets or sets the name of the control that is used by accessibility client applications.</summary>
      <returns>A <see cref="T:System.String" /> representing the name of the control that is used by accessibility client applications. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.AccessibleRole">
      <summary>Gets or sets the accessible role of the control.</summary>
      <returns>One of the values of <see cref="T:System.Windows.Forms.AccessibleRole" />. The default is <see cref="F:System.Windows.Forms.AccessibleRole.Default" />.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value assigned is not one of the <see cref="T:System.Windows.Forms.AccessibleRole" /> values.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Anchor">
      <summary>Gets or sets the edges of the container to which a shape is bound, and determines how a shape is resized when its parent is resized.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Windows.Forms.AnchorStyles" /> values. The default is Top and Left.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.BorderColor">
      <summary>Gets or sets the color of the border of a shape or line control.</summary>
      <returns>A <see cref="T:System.Drawing.Color" /> structure representing the color of the border of the shape or line. The default is the value of <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.DefaultBorderColor" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.BorderStyle">
      <summary>Gets or sets the border style for a shape or line control. </summary>
      <returns>A <see cref="T:System.Drawing.Drawing2D.DashStyle" /> value that represents the appearance of the border. The default value is Solid.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The specified value when you set this property is not a valid <see cref="T:System.Drawing.Drawing2D.DashStyle" /> value.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.BorderWidth">
      <summary>Gets or sets the width of a line or shape control's border.</summary>
      <returns>An Integer representing the border width in pixels. The default value is 1.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.BringToFront">
      <summary>Brings a line or shape control to the front of the z-order.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.CanFocus">
      <summary>Gets a value indicating whether a line or shape control can receive focus.</summary>
      <returns>true if the control can receive focus; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.CanSelect">
      <summary>Gets a value indicating whether a line or shape control can be selected.</summary>
      <returns>true if the control can be selected; otherwise, false. The default is true.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.ChangeUICues">
      <summary>Occurs when the focus or keyboard user interface (UI) cues change.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.Click">
      <summary>Occurs when the shape is clicked.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Container">
      <summary>Gets the <see cref="T:System.ComponentModel.IContainer" /> that contains the <see cref="T:System.ComponentModel.Component" />.</summary>
      <returns>A null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.ContainsFocus">
      <summary>Gets a value indicating whether a line or shape control currently has the input focus.</summary>
      <returns>true if the control currently has the input focus; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenu">
      <summary>Gets or sets the shortcut menu associated with a line or shape control.</summary>
      <returns>A <see cref="T:System.Windows.Forms.ContextMenu" /> that represents the shortcut menu for the control, or a null reference (Nothing in Visual Basic) if there is no <see cref="T:System.Windows.Forms.ContextMenu" /> assigned. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenuChanged">
      <summary>Occurs when the value of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenu" /> property changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenuStrip">
      <summary>Gets or sets the <see cref="T:System.Windows.Forms.ContextMenuStrip" /> associated with a line or shape control.</summary>
      <returns>The <see cref="T:System.Windows.Forms.ContextMenuStrip" /> for the control, or a null reference (Nothing in Visual Basic) if there is no <see cref="T:System.Windows.Forms.ContextMenuStrip" /> assigned. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenuStripChanged">
      <summary>Occurs when the value of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenuStrip" /> property changes.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.CreateAccessibilityInstance">
      <summary>Creates a new accessibility object for a line or shape control.</summary>
      <returns>Returns an <see cref="T:System.Windows.Forms.AccessibleObject" /> class.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Created">
      <summary>Gets a value indicating whether a line or shape control has been created.</summary>
      <returns>true if the control has been created; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Cursor">
      <summary>Gets or sets the cursor that is displayed when the mouse pointer is resting on a line or shape control.</summary>
      <returns>A <see cref="T:System.Windows.Forms.Cursor" /> that represents the cursor to display when the mouse pointer is resting on the control.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.CursorChanged">
      <summary>Occurs when the value of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.Cursor" /> property changes.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.DefaultBorderColor">
      <summary>Gets the default border color for a line or shape control.</summary>
      <returns>The default border <see cref="T:System.Drawing.Color" /> of the control. The default is <see cref="P:System.Windows.Forms.Control.DefaultForeColor" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by a line or shape control and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Disposing">
      <summary>Gets a value indicating whether the base <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> class is disposing.</summary>
      <returns>true if the base <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> class is disposing; otherwise, false.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.DoubleClick">
      <summary>Occurs when the shape is double-clicked.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.DrawToBitmap(System.Drawing.Bitmap,System.Drawing.Rectangle)">
      <summary>Supports rendering to the specified bitmap.</summary>
      <param name="bitmap">The <see cref="T:System.Drawing.Bitmap" /> to be drawn to.</param>
      <param name="targetBounds">The <see cref="T:System.Drawing.Rectangle" /> within which the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> is rendered.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Enabled">
      <summary>Gets or sets a value indicating whether a line or shape control can respond to user interaction.</summary>
      <returns>true if the control can respond to user interaction; otherwise, false. The default is true.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.EnabledChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.Enabled" /> property value has changed.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.Enter">
      <summary>Occurs when the shape is entered.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.FindForm">
      <summary>Retrieves the form that a line or shape control is on.</summary>
      <returns>The <see cref="T:System.Windows.Forms.Form" /> that the control is on.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Focus">
      <summary>Sets input focus to a line or shape control.</summary>
      <returns>true if the input focus request was successful; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Focused">
      <summary>Gets a value indicating whether a line or shape control currently has the input focus.</summary>
      <returns>true if the control currently has the input focus; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.GetContainerControl">
      <summary>Returns the next <see cref="T:System.Windows.Forms.ContainerControl" /> up in a line or shape control's chain of parent controls.</summary>
      <returns>An <see cref="T:System.Windows.Forms.IContainerControl" /> that represents the parent of the control.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.GotFocus">
      <summary>Occurs when the shape receives focus.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Hide">
      <summary>Conceals a line or shape control from the user.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.HitTest(System.Int32,System.Int32)">
      <summary>Gets information about a line or shape control at the specified position on the screen.</summary>
      <returns>true if the line or shape control is located at the specified coordinates; otherwise, false.</returns>
      <param name="x">The horizontal screen coordinate.</param>
      <param name="y">The vertical screen coordinate.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Invalidate">
      <summary>Invalidates a specific region of a line or shape control and causes a paint message to be sent to the control.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.Invalidated">
      <summary>Occurs when a shape's display requires redrawing.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.IsAccessible">
      <summary>Gets or sets a value indicating whether a line or shape control is available to accessibility applications.</summary>
      <returns>true if the control is available to accessibility applications; otherwise, false. The default value is true.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.IsDisposed">
      <summary>Gets a value indicating whether a line or shape control has been disposed of.</summary>
      <returns>true if the control has been disposed of; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.IsKeyLocked(System.Windows.Forms.Keys)">
      <summary>Determines whether the CAPS LOCK, NUM LOCK, or SCROLL LOCK key is in effect.</summary>
      <returns>true if the specified key or keys are in effect; otherwise, false.</returns>
      <param name="keyVal">The CAPS LOCK, NUM LOCK, or SCROLL LOCK member of the <see cref="T:System.Windows.Forms.Keys" /> enumeration.</param>
      <exception cref="T:System.NotSupportedException">The <paramref name="keyVal" /> parameter refers to a key other than the CAPS LOCK, NUM LOCK, or SCROLL LOCK key.</exception>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyDown">
      <summary>Occurs when a key is pressed and the shape has focus.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyPress">
      <summary>Occurs when a key is pressed and the shape has focus.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyUp">
      <summary>Occurs when a key is released and the shape has focus.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.Leave">
      <summary>Occurs when the input focus leaves the shape.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.LostFocus">
      <summary>Occurs when the shape loses focus.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.ModifierKeys">
      <summary>Gets a value indicating which of the modifier keys (SHIFT, CTRL, and ALT) is in a pressed state.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Windows.Forms.Keys" /> values. The default is None.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.MouseButtons">
      <summary>Gets a value indicating which of the mouse buttons is in a pressed state.</summary>
      <returns>A bitwise combination of the <see cref="T:System.Windows.Forms.MouseButtons" /> enumeration values. The default is None.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseClick">
      <summary>Occurs when the shape is clicked by the mouse.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseDoubleClick">
      <summary>Occurs when the shape is double-clicked by the mouse.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseDown">
      <summary>Occurs when the mouse pointer is over the shape and a mouse button is pressed.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseEnter">
      <summary>Occurs when the mouse pointer enters the shape.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseHover">
      <summary>Occurs when the mouse pointer rests on the shape.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseLeave">
      <summary>Occurs when the mouse pointer leaves the shape. </summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseMove">
      <summary>Occurs when the mouse pointer is moved over the shape.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.MousePosition">
      <summary>Gets the position of the pointer in screen coordinates.</summary>
      <returns>The MousePosition property returns a <see cref="T:System.Drawing.Point" /> that represents the pointer position at the time the property was referenced.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseUp">
      <summary>Occurs when the mouse pointer is over the shape and a mouse button is released.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseWheel">
      <summary>Occurs when the mouse wheel moves and the shape has focus.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.Move">
      <summary>Occurs when the shape is moved.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Name">
      <summary>Gets or sets the name of a line or shape control.</summary>
      <returns>The name of the control. The default is an empty string ("").</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnClick(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Click" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnContextMenuChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenuChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnContextMenuStripChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.ContextMenuStripChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnCursorChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.CursorChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnDoubleClick(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.DoubleClick" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnEnabledChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.EnabledChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnEnter(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Enter" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnGotFocus(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.GotFocus" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnInvalidated(System.Windows.Forms.InvalidateEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Invalidated" /> event.</summary>
      <param name="e">An <see cref="T:System.Windows.Forms.InvalidateEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyDown" /> event.</summary>
      <param name="e">A <see cref="T:System.Windows.Input.KeyEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyPress" /> event.</summary>
      <param name="e">A <see cref="T:System.Windows.Forms.KeyPressEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnKeyUp(System.Windows.Forms.KeyEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyUp" /> event.</summary>
      <param name="e">A <see cref="T:System.Windows.Input.KeyEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnLeave(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Leave" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnLostFocus(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.LostFocus" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseClick(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseClick" /> event.</summary>
      <param name="e">A <see cref="T:System.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseDoubleClick(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseDoubleClick" /> event.</summary>
      <param name="e">A <see cref="T:System.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseDown" /> event.</summary>
      <param name="e">A <see cref="T:System.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseEnter(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseEnter" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseHover(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseHover" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseLeave(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseLeave" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseMove" /> event.</summary>
      <param name="e">A <see cref="T:System.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseUp" /> event.</summary>
      <param name="e">A <see cref="T:System.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.MouseWheel" /> event.</summary>
      <param name="e">An <see cref="T:System.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnMove(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Move" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Paint" /> event.</summary>
      <param name="e">A <see cref="T:System.PaintEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnParentChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.ParentChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnPreviewKeyDown(System.Windows.Forms.PreviewKeyDownEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.PreviewKeyDown" /> event.</summary>
      <param name="e">A <see cref="T:System.PreviewKeyDownEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnQueryAccessibilityHelp(System.Windows.Forms.QueryAccessibilityHelpEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.QueryAccessibilityHelp" /> event.</summary>
      <param name="e">A <see cref="T:System.QueryAccessibilityHelpEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnRegionChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.RegionChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.OnVisibleChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.VisibleChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.Paint">
      <summary>Occurs when the shape is redrawn.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Parent">
      <summary>Gets or sets the parent container of a line or shape control.</summary>
      <returns>A <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> that represents the parent or container of the control.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.ParentChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.Parent" /> property value changes.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.PointToClient(System.Drawing.Point)">
      <summary>Computes the location of the specified screen point into client coordinates.</summary>
      <returns>A <see cref="T:System.Drawing.Point" /> that represents the converted <see cref="T:System.Drawing.Point" />, <paramref name="p" />, in client coordinates.</returns>
      <param name="p">The screen coordinate <see cref="T:System.Drawing.Point" /> to convert.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.PointToScreen(System.Drawing.Point)">
      <summary>Computes the location of the specified client point into screen coordinates.</summary>
      <returns>A <see cref="T:System.Drawing.Point" /> that represents the converted <see cref="T:System.Drawing.Point" />, <paramref name="p" />, in screen coordinates.</returns>
      <param name="p">The client coordinate <see cref="T:System.Drawing.Point" /> to convert.</param>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.PreviewKeyDown">
      <summary>Occurs before the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.KeyDown" /> event when a key is pressed and focus is on the shape.</summary>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.QueryAccessibilityHelp">
      <summary>Occurs when an <see cref="T:System.Windows.Forms.AccessibleObject" /> is providing Help to accessibility applications. </summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.RectangleToClient(System.Drawing.Rectangle)">
      <summary>Computes the size and location of the specified screen rectangle in client coordinates.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> that represents the converted <see cref="T:System.Drawing.Rectangle" />, <paramref name="p" />, in client coordinates.</returns>
      <param name="rect">The screen coordinate <see cref="T:System.Drawing.Rectangle" /> to convert.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.RectangleToScreen(System.Drawing.Rectangle)">
      <summary>Computes the size and location of the specified client rectangle in screen coordinates.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> that represents the converted <see cref="T:System.Drawing.Rectangle" />, <paramref name="p" />, in screen coordinates.</returns>
      <param name="rect">The screen coordinate <see cref="T:System.Drawing.Rectangle" /> to convert.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Refresh">
      <summary>Forces a control to invalidate its client area and immediately redraw itself and any child controls.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Region">
      <summary>Gets or sets the window region associated with a line or shape control.</summary>
      <returns>The window <see cref="T:System.Drawing.Region" /> associated with the control.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.RegionChanged">
      <summary>Occurs when the value of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.Region" /> property changes.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.ResumePaint(System.Boolean)">
      <summary>Resumes usual painting logic, optionally forcing an immediate layout of pending paint requests.</summary>
      <param name="performPaint">true to execute pending paint requests; otherwise, false.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Scale(System.Drawing.SizeF)">
      <summary>Scales a shape by the specified scaling factor.</summary>
      <param name="factor">A <see cref="T:System.Drawing.SizeF" /> that contains the horizontal and vertical scaling factors.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Select">
      <summary>Enables a control.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.SelectionColor">
      <summary>Gets or sets the selection color of a shape.</summary>
      <returns>A <see cref="T:System.Drawing.Color" /> that represents the color of the focus rectangle when a shape is selected at run time. The default is <see cref="P:System.Drawing.SystemColors.Highlight" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.SendToBack">
      <summary>Sends a line or shape control to the back of the z-order.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Show">
      <summary>Displays a shape to the user.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.SuspendPaint">
      <summary>Suspends the painting logic for a shape.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Tag">
      <summary>Gets or sets the object that contains data about a line or shape control.</summary>
      <returns>An <see cref="T:System.Object" /> that contains data associated with the control. The default is a null reference (Nothing in Visual Basic).</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Shape.Update">
      <summary>Causes the control to redraw the invalidated regions within its client area.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.UseWaitCursor">
      <summary>Gets or sets a value indicating whether to use the wait cursor for the current line or shape control.</summary>
      <returns>true to use the wait cursor for the current control; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Shape.Visible">
      <summary>Gets or sets a value indicating whether a line or shape control is displayed.</summary>
      <returns>true if the control is displayed; otherwise, false. The default is true.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Shape.VisibleChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.Shape.Visible" /> property value changes.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection">
      <summary>Represents a collection of <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> objects.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.#ctor(Microsoft.VisualBasic.PowerPacks.ShapeContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> class.</summary>
      <param name="owner">The <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> that contains the collection.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Add(Microsoft.VisualBasic.PowerPacks.Shape)">
      <summary>Adds the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <param name="value">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to add to the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#IList#Add(System.Object)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.AddRange(Microsoft.VisualBasic.PowerPacks.Shape[])">
      <summary>Adds an array of <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> objects to the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <param name="shapes">An array of <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> objects to add to the collection.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Clear">
      <summary>Removes all shapes from the collection.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Contains(Microsoft.VisualBasic.PowerPacks.Shape)">
      <summary>Determines whether the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> is a member of the collection.</summary>
      <returns>true if the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> is a member of the collection; otherwise, false.</returns>
      <param name="value">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to locate in the collection.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#IList#Contains(System.Object)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.ContainsKey(System.String)">
      <summary>Determines whether the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> contains an item with the specified key.</summary>
      <returns>true if the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> contains an item with the specified key; otherwise, false.</returns>
      <param name="key">The key to locate in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />. </param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.CopyTo(Microsoft.VisualBasic.PowerPacks.Shape[],System.Int32)">
      <summary>Copies the whole <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> to a compatible one-dimensional <see cref="T:System.Array" />, starting at the specified index of the destination array.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />. The <see cref="T:System.Array" /> must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying starts.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Count">
      <summary>Gets the number of shapes in the collection.</summary>
      <returns>An Integer representing the number of shapes in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Dispose">
      <summary>Releases the unmanaged resources used by the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.GetChildIndex(Microsoft.VisualBasic.PowerPacks.Shape)">
      <summary>Retrieves the index of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <returns>A zero-based index value that represents the location of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</returns>
      <param name="child">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to search for in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="child" /> shape is not in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.GetChildIndex(Microsoft.VisualBasic.PowerPacks.Shape,System.Boolean)">
      <summary>Retrieves the index of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />, and optionally raises an exception if the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> is not in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <returns>A zero-based index value that represents the location of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />. Or -1 if the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> is not found in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</returns>
      <param name="child">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to search for in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</param>
      <param name="throwException">true to throw an exception if the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> specified in the <paramref name="child" /> parameter is not a control in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />; otherwise, false. </param>
      <exception cref="T:System.ArgumentException">The <paramref name="child" /> shape is not in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> and the <paramref name="throwException" /> parameter value is true.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.GetEnumerator">
      <summary>Retrieves a reference to an enumerator object that is used to iterate over a <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />. </summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.IndexOf(Microsoft.VisualBasic.PowerPacks.Shape)">
      <summary>Retrieves the index of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <returns>A zero-based index value that represents the position of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</returns>
      <param name="value">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to locate in the collection.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#IList#IndexOf(System.Object)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.IndexOfKey(System.String)">
      <summary>Retrieves the index of the first occurrence of the specified item in the collection.</summary>
      <returns>The zero-based index of the first occurrence of the shape that has the specified name in the collection.</returns>
      <param name="key">The name of the shape to search for.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#IList#Insert(System.Int32,System.Object)"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.IsReadOnly">
      <summary>Gets a value indicating whether a collection is read-only.</summary>
      <returns>true if the collection is read-only; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> at the specified indexed location in the collection.</summary>
      <returns>The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> located at the specified index location in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</returns>
      <param name="index">The index of the shape to retrieve from the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Owner">
      <summary>Gets the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> that owns the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> that owns the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.Remove(Microsoft.VisualBasic.PowerPacks.Shape)">
      <summary>Removes the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> from the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <param name="value">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to remove from the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#IList#Remove(System.Object)"></member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.RemoveAt(System.Int32)">
      <summary>Removes a <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> from the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> at the specified indexed location.</summary>
      <param name="index">The index value of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to remove.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeCollection.SetChildIndex(Microsoft.VisualBasic.PowerPacks.Shape,System.Int32)">
      <summary>Sets the index of the specified <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> to the specified index value.</summary>
      <param name="child">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to search for.</param>
      <param name="newIndex">The new index value of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" />.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="child" /> shape is not in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeCollection.System#Collections#ICollection#SyncRoot"></member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer">
      <summary>Provides a container for <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" />, <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" />, <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> controls, and for any other control that derives from <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" />.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeContainer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> class.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeContainer.AccessibleRole">
      <summary>Gets or sets the accessible role of the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />.</summary>
      <returns>One of the values of <see cref="T:System.Windows.Forms.AccessibleRole" />. The default is Default.</returns>
      <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value assigned is not one of the <see cref="T:System.Windows.Forms.AccessibleRole" /> values.</exception>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeContainer.Cursor">
      <summary>Gets or sets the cursor that is displayed when the mouse pointer is over a <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />.</summary>
      <returns>A <see cref="T:System.Windows.Forms.Cursor" /> that represents the cursor to display when the mouse pointer is over the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeContainer.Dock">
      <summary>Gets or sets which control borders are docked to the parent control and determines how a control is resized when its parent is resized.</summary>
      <returns>One of the <see cref="T:System.Windows.Forms.DockStyle" /> values. The default is <see cref="F:System.Windows.Forms.DockStyle.Fill" />.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeContainer.GetChildAtPoint(System.Drawing.Point)">
      <summary>Gets the shape that is located at the specified coordinates.</summary>
      <returns>A <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> that represents the shape that is located at the specified point.</returns>
      <param name="pt">A <see cref="T:System.Drawing.Point" /> that contains the coordinates of the location where you want to look for a shape. Coordinates are expressed relative to the upper-left corner of the screen.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeContainer.GetNextShape(Microsoft.VisualBasic.PowerPacks.Shape,System.Boolean)">
      <summary>Retrieves the next or previous shape in the order of the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <returns>The next <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> in the order of the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</returns>
      <param name="shape">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to start the search with.</param>
      <param name="forward">true to search forward; false to search backward.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.ShapeContainer.SelectNextShape(Microsoft.VisualBasic.PowerPacks.Shape,System.Boolean,System.Boolean)">
      <summary>Selects the next or previous shape in the order of the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" />.</summary>
      <returns>true if a shape was enabled; otherwise, false.</returns>
      <param name="shape">The <see cref="T:Microsoft.VisualBasic.PowerPacks.Shape" /> to start the search with.</param>
      <param name="forward">true to move forward in the order; false to move backward.</param>
      <param name="wrap">true to continue searching from the first shape in the order after the last shape is reached; otherwise, false.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.ShapeContainer.Shapes">
      <summary>Gets the collection of shapes that are contained in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />.</summary>
      <returns>A <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeCollection" /> representing the collection of shapes that are contained in the <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.SimpleShape">
      <summary>Implements the basic functionality that is common to <see cref="T:Microsoft.VisualBasic.PowerPacks.OvalShape" /> and <see cref="T:Microsoft.VisualBasic.PowerPacks.RectangleShape" /> controls.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.#ctor(Microsoft.VisualBasic.PowerPacks.ShapeContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" /> class, specifying a <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" /> as its parent.</summary>
      <param name="parent">The <see cref="T:Microsoft.VisualBasic.PowerPacks.ShapeContainer" />  that is to be the parent of the shape.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" /> class, specifying its size and location.</summary>
      <param name="x">An Integer that represents the left edge (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" />.</param>
      <param name="y">An Integer that represents the top (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" />.</param>
      <param name="width">An Integer that represents the width (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" />.</param>
      <param name="height">An Integer that represents the height (in pixels) of the <see cref="T:Microsoft.VisualBasic.PowerPacks.SimpleShape" />.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackColor">
      <summary>Gets or sets the background color for the shape.</summary>
      <returns>A <see cref="T:System.Drawing.Color" /> that represents the background color of the shape. The default is the value of the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.DefaultBackColor" /> property.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackColorChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackColor" /> property of the shape is changed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImage">
      <summary>Gets or sets the image displayed in the shape.</summary>
      <returns>An <see cref="T:System.Drawing.Image" /> that represents the image to display in the background of the shape.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImageChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImage" /> property of a shape is changed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImageLayout">
      <summary>Gets or sets the background image layout as defined in the <see cref="T:System.Windows.Forms.ImageLayout" /> enumeration.</summary>
      <returns>One of the values of <see cref="T:System.Windows.Forms.ImageLayout" /> (Center,None, Stretch, Tile, or Zoom). Tile is the default value.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImageLayoutChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImageLayout" /> property of a shape is changed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackStyle">
      <summary>Gets or sets the transparency of the shape.</summary>
      <returns>One of the values of <see cref="T:Microsoft.VisualBasic.PowerPacks.BackStyle" /> (<see cref="F:Microsoft.VisualBasic.PowerPacks.BackStyle.Opaque" /> or <see cref="F:Microsoft.VisualBasic.PowerPacks.BackStyle.Transparent" />). The default is <see cref="F:Microsoft.VisualBasic.PowerPacks.BackStyle.Transparent" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.BorderWidth">
      <summary>Gets or sets the width of the shape control's border.</summary>
      <returns>An Integer representing the border width in pixels. The default value is 1.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Bottom">
      <summary>Gets the distance, in pixels, between the bottom edge of the shape and the top edge of its container's client area.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the distance, in pixels, between the bottom edge of the shape and the top edge of its container's client area.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Bounds">
      <summary>Gets or sets the size and location of the shape in pixels, relative to the parent control.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> in pixels, relative to the parent control, that represents the size and location of the shape.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.ClientRectangle">
      <summary>Gets the rectangle that represents the client area of the shape.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> in pixels, relative to the parent control, that represents the size and location of the shape.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.ClientSize">
      <summary>Gets or sets the height and width of the client area of the shape.</summary>
      <returns>A <see cref="T:System.Drawing.Size" /> that represents the dimensions of the client area of the shape.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.ClientSizeChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.ClientSize" /> property of a shape is changed.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.CreateAccessibilityInstance">
      <summary>Creates a new accessibility object for the shape control.</summary>
      <returns>Returns an <see cref="T:System.Windows.Forms.AccessibleObject" /> class.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.DefaultBackColor">
      <summary>Gets the default background color of the shape.</summary>
      <returns>The default background <see cref="T:System.Drawing.Color" /> of the shape. The default is <see cref="P:System.Windows.Forms.Control.DefaultBackColor" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.DefaultFillColor">
      <summary>Gets the default fill color of the shape.</summary>
      <returns>The default fill <see cref="T:System.Drawing.Color" /> of the shape. The default is <see cref="P:System.Windows.Forms.Control.DefaultForeColor" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.DefaultFillGradientColor">
      <summary>Gets the default fill gradient color of the shape.</summary>
      <returns>The default fill gradient <see cref="T:System.Drawing.Color" /> of the shape. The default is <see cref="P:System.Drawing.Color.LightGray" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.DisplayRectangle">
      <summary>Gets the rectangle that represents the display area of the shape.</summary>
      <returns>A <see cref="T:System.Drawing.Rectangle" /> that represents the display area of the shape .</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.DrawToBitmap(System.Drawing.Bitmap,System.Drawing.Rectangle)">
      <summary>Supports rendering to the specified bitmap.</summary>
      <param name="bitmap">The <see cref="T:System.Drawing.Bitmap" /> to be drawn to.</param>
      <param name="targetBounds">The <see cref="T:System.Drawing.Rectangle" /> within which the <see cref="T:Microsoft.VisualBasic.PowerPacks.LineShape" /> is rendered.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillColor">
      <summary>Gets or sets the color that is used to fill the shape.</summary>
      <returns>The <see cref="T:System.Drawing.Color" /> that is used to fill the shape.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientColor">
      <summary>Gets or sets the gradient color that is used to fill the shape.</summary>
      <returns>The <see cref="T:System.Drawing.Color" /> that is used for a gradient fill.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientStyle">
      <summary>Gets or sets the gradient style that is used to fill the shape.</summary>
      <returns>A <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillGradientStyle" /> enumeration that determines the type and direction of a gradient fill displayed on the shape.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.FillStyle">
      <summary>Gets or sets the pattern used to fill the shape.</summary>
      <returns>A <see cref="T:Microsoft.VisualBasic.PowerPacks.FillStyle" /> enumeration that determines the pattern to be displayed in the shape.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Height">
      <summary>Gets or sets the height of the shape.</summary>
      <returns>The height of the shape in pixels.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.HitTest(System.Int32,System.Int32)">
      <summary>Determines whether a shape control is located at the specified point on the screen.</summary>
      <returns>true if the shape control is located at the specified coordinates; otherwise, false.</returns>
      <param name="x">The horizontal screen coordinate.</param>
      <param name="y">The vertical screen coordinate.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Left">
      <summary>Gets or sets the distance, in pixels, between the left edge of the shape and the left edge of its container's client area.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the distance, in pixels, between the left edge of the shape and the left edge of its container's client area.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Location">
      <summary>Gets or sets the coordinates of the upper-left corner of the shape relative to the upper-left corner of its container.</summary>
      <returns>The <see cref="T:System.Drawing.Point" /> that represents the upper-left corner of the shape relative to the upper-left corner of its container.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.LocationChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Location" /> property of a shape is changed.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnBackColorChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackColorChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnBackgroundImageChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImageChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnBackgroundImageLayoutChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.BackgroundImageLayoutChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnClientSizeChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.ClientSizeChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnLocationChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.LocationChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Shape.Paint" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnResize(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.Resize" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.OnSizeChanged(System.EventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.SizeChanged" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.Resize">
      <summary>Occurs when the size of a shape is changed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Right">
      <summary>Gets the distance, in pixels, between the right edge of the shape and the left edge of its container's client area.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the distance, in pixels, between the right edge of the shape and the left edge of its container's client area.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.Scale(System.Drawing.SizeF)">
      <summary>Scales the shape by the specified scaling factor.</summary>
      <param name="factor">A <see cref="T:System.Drawing.SizeF" /> that contains the horizontal and vertical scaling factors.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.SimpleShape.SetBounds(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Sets the bounds of the shape to the specified location and size.</summary>
      <param name="x">The new Left property value of the shape. </param>
      <param name="y">The new Top property value of the shape. </param>
      <param name="width">The new Width property value of the shape. </param>
      <param name="height">The new Height property value of the shape. </param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Size">
      <summary>Gets or sets the height and width of the shape.</summary>
      <returns>The <see cref="T:System.Drawing.Size" /> that represents the height and width of the shape in pixels.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.SimpleShape.SizeChanged">
      <summary>Occurs when the <see cref="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Size" /> property of a shape is changed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Top">
      <summary>Gets or sets the distance, in pixels, between the top edge of the shape and the left edge of its container's client area.</summary>
      <returns>An <see cref="T:System.Int32" /> that represents the distance, in pixels, between the top edge of the control and the top edge of its container's client area.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.SimpleShape.Width">
      <summary>Gets or sets the width of the shape.</summary>
      <returns>The width of the shape in pixels.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm">
      <summary>Enables printing an image of a Windows Form at run time.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.#ctor(System.ComponentModel.IContainer)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm" /> class, specifying the <see cref="T:System.ComponentModel.IContainer" /> in which it will be parented.</summary>
      <param name="container">A <see cref="T:System.ComponentModel.IContainer" /> in which the component will be parented.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.#ctor(System.Windows.Forms.Form)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm" /> class, specifying the <see cref="T:System.Windows.Forms.Form" /> where it will be parented.</summary>
      <param name="parentForm">A <see cref="T:System.Windows.Forms.Form" /> where the component will be parented.</param>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.BeginPrint">
      <summary>Occurs when the <see cref="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.Print" /> method is called, before the first page of the document prints.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by a <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm" /> component and optionally releases the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.DocumentName">
      <summary>Gets or sets the document name to display (for example, in a print status dialog box or printer queue) while printing the document.</summary>
      <returns>A String to display while printing the document. The default is "document".</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.EndPrint">
      <summary>Occurs when the last page of the document has printed.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.Form">
      <summary>Gets or sets the form to be printed.</summary>
      <returns>Returns a <see cref="T:System.Windows.Forms.Form" /> object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.OnBeginPrint(System.Drawing.Printing.PrintEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.BeginPrint" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.OnEndPrint(System.Drawing.Printing.PrintEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.EndPrint" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.OnQueryPageSettings(System.Drawing.Printing.QueryPageSettingsEventArgs)">
      <summary>Raises the <see cref="E:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.QueryPageSettings" /> event.</summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.Print">
      <summary>Sends an image of a Windows Form to the destination specified by the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintAction" /> property.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.Print(System.Windows.Forms.Form,Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption)">
      <summary>Sends an image of a Windows Form to the destination specified by the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintAction" /> property, specifying the form and printing options</summary>
      <param name="printForm">The name of the <see cref="T:System.Windows.Forms.Form" /> to be printed.</param>
      <param name="printFormOption">A <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption" /> enumeration that determines how the form will be printed.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintAction">
      <summary>Gets or sets a value indicating whether the output is directed to a printer, to a print preview window, or to a file.</summary>
      <returns>Returns a <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintAction" /> enumeration.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrinterSettings">
      <summary>Gets or sets a <see cref="T:System.Drawing.Printing.PrinterSettings" /> object that you can use to specify various properties of a printer.</summary>
      <returns>Returns a <see cref="T:System.Drawing.Printing.PrinterSettings" /> object. </returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintFileName">
      <summary>Gets or sets the file name of an Encapsulated PostScript file and the path to which the file will be saved when the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintAction" /> property is set to <see cref="F:System.Drawing.Printing.PrintAction.PrintToFile" />.</summary>
      <returns>Returns a String that contains a file path and name.</returns>
    </member>
    <member name="E:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.QueryPageSettings">
      <summary>Occurs immediately before each page is printed.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption">
      <summary>The PrintOption enumeration specifies options for the <see cref="M:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.Print(System.Windows.Forms.Form,Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption)" /> method of a <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm" /> component.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption.CompatibleModeClientAreaOnly">
      <summary>Uses the compatible printing implementation to print the currently visible client area.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption.CompatibleModeFullWindow">
      <summary>Uses the compatible printing implementation to print the currently visible form. This includes the title bar, scroll bars, and border.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption.ClientAreaOnly">
      <summary>Uses a new implementation to print the currently visible client area.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption.FullWindow">
      <summary>Uses a new implementation to print the currently visible form. This includes the title bar, scroll bars, and border.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.PrintForm.PrintOption.Scrollable">
      <summary>Uses a new implementation to print the full client area, even if part of it is scrolled out of view.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants">
      <summary>Provides constants that map Visual Basic 6.0 color constants to their <see cref="T:System.Drawing.Color" /> equivalents.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbBlack">
      <summary>Maps the Visual Basic 6.0 constant vbBlack to <see cref="P:System.Drawing.Color.Black" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbBlue">
      <summary>Maps the Visual Basic 6.0 constant vbBlue to <see cref="P:System.Drawing.Color.Blue" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbCyan">
      <summary>Maps the Visual Basic 6.0 constant vbCyan to <see cref="P:System.Drawing.Color.Cyan" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbGreen">
      <summary>Maps the Visual Basic 6.0 constant vbGreen to <see cref="P:System.Drawing.Color.Green" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbMagenta">
      <summary>Maps the Visual Basic 6.0 constant vbMagenta to <see cref="P:System.Drawing.Color.Magenta" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbRed">
      <summary>Maps the Visual Basic 6.0 constant vbRed to <see cref="P:System.Drawing.Color.Red" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbWhite">
      <summary>Maps the Visual Basic 6.0 constant vbWhite to <see cref="P:System.Drawing.Color.White" />.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ColorConstants.vbYellow">
      <summary>Maps the Visual Basic 6.0 constant vbYellow to <see cref="P:System.Drawing.Color.Yellow" />.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.DrawStyleConstants">
      <summary>Provides constants that map Visual Basic 6.0 DrawStyle constants to their <see cref="P:System.Drawing.Pen.DashStyle" /> equivalents.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.DrawStyleConstants.vbDash">
      <summary>Maps the Visual Basic 6.0 constant vbDash to <see cref="P:System.Drawing.Pen.DashStyle" /> Dash.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.DrawStyleConstants.vbDashDot">
      <summary>Maps the Visual Basic 6.0 constant vbDashDot to <see cref="P:System.Drawing.Pen.DashStyle" /> DashDot.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.DrawStyleConstants.vbDashDotDot">
      <summary>Maps the Visual Basic 6.0 constant vbDashDotDot to <see cref="P:System.Drawing.Pen.DashStyle" /> DashDotDot.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.DrawStyleConstants.vbDot">
      <summary>Maps the Visual Basic 6.0 constant vbDot to <see cref="P:System.Drawing.Pen.DashStyle" /> Dot.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.DrawStyleConstants.vbSolid">
      <summary>Maps the Visual Basic 6.0 constant vbDash to <see cref="P:System.Drawing.Pen.DashStyle" /> Solid.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants">
      <summary>Provides constants that map Visual Basic 6.0 FillStyle constants to their <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> equivalents.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbCross">
      <summary>Maps the Visual Basic 6.0 constant vbCross to <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> Cross.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbDiagonalCross">
      <summary>Maps the Visual Basic 6.0 constant vbDiagonalCross to <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> DiagonalCross.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbDownwardDiagonal">
      <summary>Maps the Visual Basic 6.0 constant vbDownwardDiagonal to <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> BackwardDiagonal.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbFSSolid">
      <summary>Maps the Visual Basic 6.0 constant vbFSSolid to a solid color.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbFSTransparent">
      <summary>Maps the Visual Basic 6.0 constant vbFSTransparent to an absence of patterns.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbHorizontalLine">
      <summary>Maps the Visual Basic 6.0 constant vbHorizontalLine to <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> Horizontal.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbUpwardDiagonal">
      <summary>Maps the Visual Basic 6.0 constant vbUpwardDiagonal to <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> ForwardDiagonal.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.FillStyleConstants.vbVerticalLine">
      <summary>Maps the Visual Basic 6.0 constant vbVerticalLine to <see cref="T:System.Drawing.Drawing2D.HatchStyle" /> Vertical.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.GlobalModule">
      <summary>This class supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.GlobalModule.Printers">
      <summary>This class supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <returns>A <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection" /> for use during upgrade.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer">
      <summary>Provides a Printer object for use by upgraded Visual Basic 6.0 printing code.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Circle(System.Boolean,System.Single,System.Single,System.Single,System.Int32,System.Single,System.Single,System.Single)">
      <summary>Prints a circle, an ellipse, or an arc on a page, specifying whether the center point is relative to the current location.</summary>
      <param name="relativeStart">Boolean. If this parameter is set to true, the center of the circle, ellipse, or arc is printed relative to the coordinates specified in the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY" /> properties of the object.</param>
      <param name="x">Single value indicating the vertical coordinate for the center point of the circle, ellipse, or arc. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measurement used.</param>
      <param name="y">Single value indicating the radius of the circle or ellipse. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measurement used.</param>
      <param name="radius">Single value indicating the radius of the circle or ellipse. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measurement used.</param>
      <param name="color">Optional. Integer value indicating the RGB (red-green-blue) color of the circle's outline. If this parameter is omitted, the value of <see cref="P:System.Drawing.Color.Black" /> is used.</param>
      <param name="startAngle">Optional. Single-precision value. When an arc or a partial circle or ellipse is printed, <paramref name="startangle" /> and <paramref name="endAngle" /> specify (in radians) the start and end positions of the arc. The range for both is 2 * pi radians to 2 * pi radians. The default value for <paramref name="startAngle" /> is 0 radians; the default for <paramref name="endAngle" /> is 2 * pi radians.</param>
      <param name="endAngle">Optional. Single-precision value. When an arc or a partial circle or ellipse is printed, <paramref name="startAngle" /> and <paramref name="endAngle" /> specify (in radians) the start and end positions of the arc. The range for both is 2 * pi radians to 2 * pi radians. The default value for <paramref name="startAngle" /> is 0 radians; the default for <paramref name="endAngle" /> is 2 * pi radians.</param>
      <param name="aspect">Optional. Single-precision value indicating the aspect ratio of the circle or ellipse. The default value is 1.0, which yields a perfect (non-elliptical) circle on any screen.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Circle(System.Single,System.Single,System.Single,System.Int32,System.Single,System.Single,System.Single)">
      <summary>Prints a circle, an ellipse, or an arc on a page.</summary>
      <param name="x">Single value indicating the horizontal coordinate for the center point of the circle, ellipse, or arc. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measurement used.</param>
      <param name="y">Single value indicating the vertical coordinate for the center point of the circle, ellipse, or arc. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measurement used.</param>
      <param name="radius">Single value indicating the radius of the circle or ellipse. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measurement used.</param>
      <param name="color">Optional. Integer value indicating the RGB (red-green-blue) color of the circle's outline. If this parameter is omitted, the value of <see cref="P:System.Drawing.Color.Black" /> is used.</param>
      <param name="startAngle">Optional. Single-precision value. When an arc or a partial circle or ellipse is printed, <paramref name="startAngle" /> and <paramref name="endAngle" /> specify (in radians) the start and end positions of the arc. The range for both is 2 * pi radians to 2 * pi radians. The default value for <paramref name="startAngle" /> is 0 radians; the default for <paramref name="endAngle" /> is 2 * pi radians.</param>
      <param name="endAngle">Optional. Single-precision value. When an arc or a partial circle or ellipse is printed, <paramref name="startAngle" /> and <paramref name="endAngle" /> specify (in radians) the start and end positions of the arc. The range for both is 2 * pi radians to 2 * pi radians. The default value for <paramref name="startAngle" /> is 0 radians; the default for <paramref name="endAngle" /> is 2 * pi radians.</param>
      <param name="aspect">Optional. Single-precision value indicating the aspect ratio of the circle or ellipse. The default value is 1.0, which yields a perfect (non-elliptical) circle on any screen.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ColorMode">
      <summary>Gets or sets a value specifying whether the output will print in color or in monochrome on a device that supports color.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Copies">
      <summary>Gets or sets a value that determines the number of copies to be printed.</summary>
      <returns>A Short representing the number of copies to be printed.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX">
      <summary>Gets or sets the horizontal coordinates for the next printing or drawing method.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY">
      <summary>Gets or sets the vertical coordinates for the next printing or drawing method.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.DeviceName">
      <summary>Gets the name of the current printer.</summary>
      <returns>Returns a String.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.DocumentName">
      <summary>Gets or sets the document name to display (for example, in a print status dialog box or printer queue) while printing the document.</summary>
      <returns>A String to display while printing the document. The default is "document".</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.DrawStyle">
      <summary>Gets or sets a value that determines the line style for output from graphics methods.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.DrawWidth">
      <summary>Gets or sets the line width for output from graphics methods.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Duplex">
      <summary>Gets or sets a value that determines whether a page is printed on both sides (if the printer supports this feature).</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.EndDoc">
      <summary>Ends a print operation sent to the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> object, releasing the document to the print device or spooler.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillColor">
      <summary>Gets or sets the color that is used to fill in shapes created by using the <see cref="Overload:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Circle" /> and <see cref="Overload:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Line" /> graphics methods.</summary>
      <returns>Returns an Integer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillStyle">
      <summary>Gets or sets the pattern used to fill shapes created by using the <see cref="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Circle(System.Boolean,System.Single,System.Single,System.Single,System.Int32,System.Single,System.Single,System.Single)" /> and <see cref="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Line(System.Boolean,System.Single,System.Single,System.Boolean,System.Single,System.Single,System.Int32,System.Boolean,System.Boolean)" /> graphics methods.</summary>
      <returns>Returns a Short. The default is 1.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Font">
      <summary>Gets or sets a FontFamily by name.</summary>
      <returns>Returns a <see cref="T:System.Drawing.Font" />.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontBold">
      <summary>Gets or sets the bold font style.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontCount">
      <summary>Returns the number of fonts available for the current display device or active printer.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontItalic">
      <summary>Gets or sets the italic font style.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontName">
      <summary>Gets or sets the name of the font in which text is displayed for a printing operation.</summary>
      <returns>Returns a String.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Fonts(System.Int32)">
      <summary>Gets all font names that are available for the current printer.</summary>
      <returns>Returns a String.</returns>
      <param name="index">The index of the font within the collection.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontSize">
      <summary>Gets or sets the size of the font that is used for text in a run-time printing operation.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontStrikethru">
      <summary>Gets or sets the strikethrough font style.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontTransparent">
      <summary>Gets or sets a value that determines whether background graphics on a Printer object are printed behind text characters.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FontUnderline">
      <summary>Gets or sets the underlined font style.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ForeColor">
      <summary>Gets or sets the color in which text and graphics are printed.</summary>
      <returns>Returns an Integer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Height">
      <summary>Gets or sets the height of a page.</summary>
      <returns>Returns an Integer.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.IsDefaultPrinter">
      <summary>Returns a value that determines whether the currently selected printer is defined as the default printer in Control Panel.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.KillDoc">
      <summary>Immediately stops the current print job.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Line(System.Boolean,System.Single,System.Single,System.Boolean,System.Single,System.Single,System.Int32,System.Boolean,System.Boolean)">
      <summary>Prints lines, squares, or rectangles on a page.</summary>
      <param name="relativeStart">Boolean. If this parameter is set to true, the starting coordinates are relative to the coordinates given by the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY" /> properties of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> object.</param>
      <param name="x1">Single value indicating the horizontal coordinate of the starting point for the line being printed.</param>
      <param name="y1">Single value indicating the vertical coordinate of the starting point for the line being printed.</param>
      <param name="relativeEnd">Boolean. If this parameter is set to true, the ending coordinates are relative to the coordinates given by the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY" /> properties of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> object.</param>
      <param name="x2">Single value indicating the horizontal coordinate of the endpoint for the line being printed. </param>
      <param name="y2">Single value indicating the vertical coordinate of the endpoint for the line being printed. </param>
      <param name="color">Optional. Integer value indicating the RGB (red-green-blue) color of the line. If this parameter is omitted, the value of <see cref="P:System.Drawing.Color.Black" /> is used.</param>
      <param name="box">Optional. Boolean. If this parameter is set to true, a rectangle is printed. The <paramref name="x1" />, <paramref name="y1" />, <paramref name="x2" />, and <paramref name="y2" /> coordinates specify opposite corners of the rectangle.</param>
      <param name="fill">Optional. Boolean. If the <paramref name="box" /> parameter is used and the <paramref name="fill" /> parameter is set to true, the rectangle is filled with the same color used to print the rectangle. you cannot use <paramref name="fill" /> without <paramref name="box" />. If <paramref name="box" /> is used without <paramref name="fill" />, the current <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillColor" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillStyle" /> values are used to fill the rectangle. The default value for <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillStyle" /> is transparent.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Line(System.Single,System.Single)">
      <summary>Prints lines on a page.</summary>
      <param name="x2">Single value indicating the horizontal coordinate of the endpoint for the line being printed. The starting point for the line is determined by the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY" /> property values.</param>
      <param name="y2">Single value indicating the vertical coordinate of the endpoint for the line being printed. The starting point for the line is determined by the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY" /> property values.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Line(System.Single,System.Single,System.Single,System.Single,System.Int32,System.Boolean,System.Boolean)">
      <summary>Prints lines, squares, or rectangles on a page.</summary>
      <param name="x1">Single value indicating the horizontal coordinate of the starting point for the line being printed. </param>
      <param name="y1">Single value indicating the vertical coordinate of the starting point for the line being printed. </param>
      <param name="x2">Single value indicating the horizontal coordinate of the endpoint for the line being printed. </param>
      <param name="y2">Single value indicating the vertical coordinate of the endpoint for the line being printed. </param>
      <param name="color">Optional. Integer value indicating the RGB (red-green-blue) color of the line. If this parameter is omitted, the value of <see cref="P:System.Drawing.Color.Black" /> is used.</param>
      <param name="box">Optional. Boolean. If this parameter is set to true, a rectangle is printed. The <paramref name="x1" />, <paramref name="y1" />, <paramref name="x2" />, and <paramref name="y2" /> coordinates specify opposite corners of the rectangle.</param>
      <param name="fill">Optional. Boolean. If the <paramref name="box" /> parameter is used and the <paramref name="fill" /> parameter is set to true, the rectangle is filled with the same color used to print the rectangle. You cannot use <paramref name="fill" /> without <paramref name="box" />. If <paramref name="box" /> is used without <paramref name="fill" />, the current <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillColor" /> and <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillStyle" /> values are used to fill the rectangle. The default value for <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.FillStyle" /> is transparent.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.NewPage">
      <summary>Stops the printing on the current page and resumes printing on a new page.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Orientation">
      <summary>Gets or sets a value indicating whether documents are printed in portrait or landscape mode.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Page">
      <summary>Returns the page number of the page that is currently being printed.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PaintPicture(System.Drawing.Image,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>Prints the contents of an image file on a page.</summary>
      <param name="picture">
        <see cref="T:System.Drawing.Image" /> value representing the image to be printed.</param>
      <param name="x1">Single value indicating the horizontal destination coordinates where the image will be printed. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measure used.</param>
      <param name="y1">Single value indicating the vertical destination coordinates where the image will be printed. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property determines the units of measure used.</param>
      <param name="width1">Optional. Single value indicating the destination width of the picture. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property of object determines the units of measurement used. If the destination width is larger or smaller than the source width, picture is stretched or compressed to fit. If omitted, the source width is used.</param>
      <param name="height1">Optional. Single value indicating the destination height of the picture. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property of object determines the units of measurement used. If the destination height is larger or smaller than the source height, picture is stretched or compressed to fit. If omitted, the source height is used.</param>
      <param name="x2">Optional. Single values indicating the coordinates (x-axis) of a clipping region within picture. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property of object determines the units of measurement used. If omitted, 0 is assumed.</param>
      <param name="y2">Optional. Single values indicating the coordinates (y-axis) of a clipping region within picture. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property of object determines the units of measurement used. If omitted, 0 is assumed.</param>
      <param name="width2">Optional. Single value indicating the source width of a clipping region within picture. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property of object determines the units of measurement used. If omitted, the entire source width is used.</param>
      <param name="height2">Optional. Single value indicating the source height of a clipping region within picture. The <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property of object determines the units of measurement used. If omitted, the entire source height is used.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PaperBin">
      <summary>Gets or sets a value indicating the default paper bin on the printer from which paper is fed during print operations.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PaperSize">
      <summary>Gets or sets a value indicating the paper size for the current printer.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Print(System.Object[])">
      <summary>Prints text to a page.</summary>
      <param name="args">A parameter array containing optional printing parameters.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PrintAction">
      <summary>Gets or sets a value that determines whether the print output is directed to a printer, to a print preview window, or to a file.</summary>
      <returns>Returns a <see cref="T:System.Drawing.Printing.PrintAction" /> enumeration.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PrintFileName">
      <summary>Gets or sets a value that specifies the file name of an Encapsulated PostScript file and the path to which the file will be saved when the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PrintAction" /> property is set to PrintToFile.</summary>
      <returns>Returns a String that contains a file path and name.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PrintQuality">
      <summary>Gets or sets a value that indicates the printer resolution.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PSet(System.Boolean,System.Single,System.Single,System.Int32)">
      <summary>Prints a single point in a specified color on a page, optionally specifying a point relative to the current coordinates.</summary>
      <param name="relativeStart">Boolean value indicating whether the coordinates are relative to the current graphics position (as set by <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentX" />, <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.CurrentY" />).</param>
      <param name="x">Single value indicating the horizontal coordinates of the point to print.</param>
      <param name="y">Single value indicating the vertical coordinates of the point to print.</param>
      <param name="color">Optional. Integer value indicating the RGB (red-green-blue) color specified for the point. If this parameter is omitted, the current <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ForeColor" /> property setting is used.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PSet(System.Single,System.Single)">
      <summary>Prints a single point on a page.</summary>
      <param name="x">Single value indicating the horizontal coordinates of the point to print.</param>
      <param name="y">Single value indicating the vertical coordinates of the point to print.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.PSet(System.Single,System.Single,System.Int32)">
      <summary>Prints a single point in a specified color on a page.</summary>
      <param name="x">Single value indicating the horizontal coordinates of the point to print.</param>
      <param name="y">Single value indicating the vertical coordinates of the point to print.</param>
      <param name="color">Optional. Integer value indicating the RGB (red-green-blue) color specified for the point. If this parameter is omitted, the current <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ForeColor" /> property setting is used.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.RightToLeft">
      <summary>Gets or sets a Boolean value that indicates the text display direction on a right-to-left system.</summary>
      <returns>Returns a Boolean.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Scale">
      <summary>Defines the coordinate system of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> object.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Scale(System.Single,System.Single,System.Single,System.Single)">
      <summary>Defines the coordinate system of the <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> object.</summary>
      <param name="x1">Single value indicating the horizontal coordinates that defines the upper-left corner of the object. Parentheses must enclose the values.</param>
      <param name="y1">Single value indicating the vertical coordinates that defines the upper-left corner of the object. Parentheses must enclose the values.</param>
      <param name="x2">Single value indicating the horizontal coordinates that defines the lower-right corner of the object. Parentheses must enclose the values.</param>
      <param name="y2">Single value indicating the vertical coordinates that defines the lower-right corner of the object. Parentheses must enclose the values.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleHeight">
      <summary>Gets or sets the number of units for the vertical measurement of the page when you use graphics methods.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleLeft">
      <summary>Gets or sets the horizontal coordinates for the left edge of the page when you are using graphics methods.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode">
      <summary>Gets or sets a value indicating the unit of measurement for the coordinates of an object when you are using graphics methods.</summary>
      <returns>Returns a Short.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleTop">
      <summary>Gets or sets the vertical coordinates for the top edge of the page when you are using graphics methods.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleWidth">
      <summary>Gets or sets the number of units for the horizontal measurement of the page when you use graphics methods.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleX(System.Single,System.Int16,System.Int16)">
      <summary>Converts the value for the width of a page from one of the units of measure of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property to another.</summary>
      <returns>Returns a Single.</returns>
      <param name="value">Specify the number of units of measure to be converted.</param>
      <param name="fromScale">Optional. A constant or value specifying the coordinate system from which the width of the object is to be converted. The possible values of <paramref name="fromScale" /> are the same as those for the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property.</param>
      <param name="toScale">Optional. A constant or value specifying the coordinate system to which the width of the object is to be converted. The possible values of <paramref name="toScale" /> are the same as those for the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleY(System.Single,System.Int16,System.Int16)">
      <summary>Converts the value for the height of a page from one of the units of measure of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property to another.</summary>
      <returns>Returns a Single.</returns>
      <param name="value">Specify the number of units of measure to be converted.</param>
      <param name="fromScale">Optional. A constant or value specifying the coordinate system from which the height of the object is to be converted. The possible values of <paramref name="fromScale" /> are the same as those for the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property.</param>
      <param name="toScale">Optional. A constant or value specifying the coordinate system to which the height of the object is to be converted. The possible values of <paramref name="toScale" /> are the same as those for the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.ScaleMode" /> property.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.TextHeight(System.String)">
      <summary>Returns the height of a text string as it would be printed in the current font.</summary>
      <returns>Returns a Single.</returns>
      <param name="text">The String to be measured.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.TextWidth(System.String)">
      <summary>Returns the width of a text string as it would be printed in the current font.</summary>
      <returns>Returns a Single</returns>
      <param name="text">The String to be measured.</param>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.TwipsPerPixelX">
      <summary>Gets a value indicating the number of twips per pixel for an object measured horizontally.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.TwipsPerPixelY">
      <summary>Gets a value indicating the number of twips per pixel for an object measured vertically.</summary>
      <returns>Returns a Single.</returns>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Width">
      <summary>Gets or sets the width of a page.</summary>
      <returns>Returns an Integer.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer.Write(System.Object[])">
      <summary>Prints text to a page without adding a carriage return.</summary>
      <param name="args">A parameter array containing optional printing parameters.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection">
      <summary>Provides a collection of printers for use by upgraded Visual Basic 6.0 printing code.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection.#ctor">
      <summary>Initializes a <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection" /> object.</summary>
    </member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection.Count">
      <summary>Returns the number of printers in the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.GlobalModule.Printers" /> collection.</summary>
      <returns>An Integer representing the number of printers in the collection.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection.System#Collections#IEnumerable#GetEnumerator"></member>
    <member name="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterCollection.Item(System.Int32)">
      <summary>Returns a specific member of the <see cref="P:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.GlobalModule.Printers" /> collection by index number.</summary>
      <returns>A <see cref="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.Printer" /> object.</returns>
      <param name="index">An Integer representing the index of the printer.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants">
      <summary>Provides constants that map Visual Basic 6.0 Printer constants to their <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalents.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNAuto">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNCassette">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNEnvelope">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNEnvManual">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNLargeCapacity">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNLargeFmt">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNLower">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNManual">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNMiddle">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNSmallFmt">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNTractor">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRBNUpper">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRCMColor">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRCMMonochrome">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRDPHorizontal">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRDPSimplex">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRDPVertical">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRORLandscape">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRORPortrait">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPQDraft">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPQHigh">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPQLow">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPQMedium">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPS10x14">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPS11x17">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSA3">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSA4">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSA4Small">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSA5">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSB4">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSB5">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSCSheet">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSDSheet">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnv10">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnv11">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnv12">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnv14">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnv9">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvB4">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvB5">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvB6">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvC3">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvC4">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvC5">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvC6">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvC65">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvDL">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvItaly">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvMonarch">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSEnvPersonal">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSESheet">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSExecutive">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSFanfoldLglGerman">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSFanfoldStdGerman">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSFanfoldUS">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSFolio">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSLedger">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSLegal">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSLetter">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSLetterSmall">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSNote">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSQuarto">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSStatement">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSTabloid">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.PrinterObjectConstants.vbPRPSUser">
      <summary>Maps the Visual Basic 6.0 Printer constant to its <see cref="T:System.Drawing.Printing.PrinterSettings" /> equivalent.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants">
      <summary>Provides constants that map Visual Basic 6.0 ScaleMode constants to their .NET Framework equivalents.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbCentimeters">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbCharacters">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbHimetric">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbInches">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbMillimeters">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbPixels">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbPoints">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbTwips">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.ScaleModeConstants.vbUser">
      <summary>Provides an equivalent for the Visual Basic 6.0 ScaleMode constant of the same name.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants">
      <summary>Provides constants that map Visual Basic 6.0 color constants to their <see cref="T:System.Drawing.SystemColors" /> equivalents.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vb3DDKShadow">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vb3DFace">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vb3DHighlight">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vb3DLight">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vb3DShadow">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbActiveBorder">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbActiveTitleBar">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbActiveTitleBarText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbApplicationWorkspace">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbButtonFace">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbButtonShadow">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbButtonText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbDesktop">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbGrayText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbHighlight">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbHighlightText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbInactiveBorder">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbInactiveCaptionText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbInactiveTitleBar">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbInactiveTitleBarText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbInfoBackground">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbInfoText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbMenuBar">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbMenuText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbScrollBars">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbTitleBarText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbWindowBackground">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbWindowFrame">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.PowerPacks.Printing.Compatibility.VB6.SystemColorConstants.vbWindowText">
      <summary>Maps the Visual Basic 6.0 SystemColor constant to its <see cref="T:System.Drawing.SystemColors" /> equivalent.</summary>
    </member>
  </members>
</doc>