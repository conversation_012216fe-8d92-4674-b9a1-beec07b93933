.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToString(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'String' is not valid.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToString(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'String' is not valid.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToString(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'String' is not valid.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToString(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'String' is not valid.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToString(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'String' is not valid.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
.Net SqlClient Data Provider
   at InsightHR.clsFinance.Dashboard_GetSalariesAnalysis(Int32 Month, Int32 Year, Int32 UserID, String GroupName, Int32 CostCenterID, Int32 IsRentedVehicles) in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Classes\clsFinance.vb:line 104
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Procedure or function 'Dashboard_GetSalariesAnalysis' expects parameter '@GroupName', which was not supplied.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToString(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'String' is not valid.
Microsoft.VisualBasic
   at Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(Object Value)
   at InsightHR.frmMain.FillDashboard() in D:\Kar Group Source Code\DS.Kar.HR\InsightHR\Main\frmMain.vb:line 203
Conversion from type 'DataRowView' to type 'Integer' is not valid.
