﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
HR
</name>
</assembly>
<members>
<member name="T:HR.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:HR.My.Resources.Resources._4373169_excel_logo_logos_icon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources._Exit">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources._New">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources._Select">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Active">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.add">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Authorize">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.bookmark">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.btnClose">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Cancel">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.caution">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.clear_filter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.CloseForm">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.connect">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Copy">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Delete">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Delete_icon_png_2">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.diskette">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.DrillThrough">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.dtpStoke">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.dtpStoke_Hide">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Edit">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.EmpDoc">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.EmpProfilePhoto">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.EmpSalary">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.EmpSalary2">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.EmpSalary21">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Excel_Icon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.export">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Filter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.filter_tool_black_shape_13_13">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Find">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Generate">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Go">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Lnk">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.loading_green">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Log">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.LoginUser">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.mail">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.NotAuthorize">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Ok">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.open_folder">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.outlook_icon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Parameter_icon_16_16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Past">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.pdf_icon2">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.play">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Post">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.query_editor_icon_png_24_24_2">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Refresh">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.refresh_arrow">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Refresh_icon_2__1_">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Remove_Filter">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.save_as">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.Search">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.text_document">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.UnPost">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.View">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.ViewIcon">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.zoom_in">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:HR.My.Resources.Resources.zoom_out">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="T:HR.dsAuditRep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsAuditRep.dtAuditRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsAuditRep.dtAuditRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsAuditRep.dtAuditRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEBank">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEBank.dtcEBankDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEBank.dtcEBankRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEBank.dtcEBankRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEBankTableAdapters.tacEBank">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEBankTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEBankTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEBank,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEBankTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEBank,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEBankTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEBank,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEBankTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEBankTableAdapters.TableAdapterManager.UpdateAll(HR.dscEBank)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEBankTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEBankTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEMajor">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEMajor.dtcEMajorDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEMajor.dtcEMajorRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEMajor.dtcEMajorRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEMajorTableAdapters.tacEMajor">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEMajorTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEMajorTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEMajor,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEMajorTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEMajor,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEMajorTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEMajor,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEMajorTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEMajorTableAdapters.TableAdapterManager.UpdateAll(HR.dscEMajor)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEMajorTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEMajorTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEVillages">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEVillages.dtcEVillagesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEVillages.dtcEVillagesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEVillages.dtcEVillagesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEVillagesTableAdapters.tacEVillages">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEVillagesTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEVillagesTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEVillages,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEVillagesTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEVillages,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEVillagesTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEVillages,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEVillagesTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEVillagesTableAdapters.TableAdapterManager.UpdateAll(HR.dscEVillages)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEVillagesTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEVillagesTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEClass">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEClass.dtcEClassDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEClass.dtcEClassRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEClass.dtcEClassRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEClassTableAdapters.tacEClass">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEClassTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEClassTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEClass,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEClassTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEClass,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEClassTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEClass,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEClassTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEClassTableAdapters.TableAdapterManager.UpdateAll(HR.dscEClass)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEClassTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEClassTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEDepartment_New">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEDepartment_New.dtcEDepartmentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEDepartment_New.dtcEDepartmentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEDepartment_New.dtcEDepartmentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEDepartment_NewTableAdapters.tacEDepartment">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEDepartment_NewTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEDepartment_New,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEDepartment_New,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEDepartment_New,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.UpdateAll(HR.dscEDepartment_New)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEDepartment_NewTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEJobCategory">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEJobCategory.dtcEJobCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEJobCategory.dtcEJobCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEJobCategory.dtcEJobCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEJobCategoryTableAdapters.tacEJobCategory">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEJobCategoryTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEJobCategoryTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEJobCategory,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEJobCategoryTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEJobCategory,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEJobCategoryTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEJobCategory,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEJobCategoryTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEJobCategoryTableAdapters.TableAdapterManager.UpdateAll(HR.dscEJobCategory)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEJobCategoryTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEJobCategoryTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEJobGrade">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEJobGrade.dtcEJobGradeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEJobGrade.dtcEJobGradeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEJobGrade.dtcEJobGradeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEJobGradeTableAdapters.tacEJobGrade">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEJobGradeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEJobGradeTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEJobGrade,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEJobGradeTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEJobGrade,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEJobGradeTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEJobGrade,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEJobGradeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEJobGradeTableAdapters.TableAdapterManager.UpdateAll(HR.dscEJobGrade)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEJobGradeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEJobGradeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEDesignation">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEDesignation.dtcEDesignationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEDesignation.dtcEDesignationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEDesignation.dtcEDesignationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEDesignationTableAdapters.tacEDesignation">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEDesignationTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEDesignationTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEDesignation,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDesignationTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEDesignation,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDesignationTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEDesignation,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEDesignationTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEDesignationTableAdapters.TableAdapterManager.UpdateAll(HR.dscEDesignation)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEDesignationTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEDesignationTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscETitle">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscETitle.dtcETitleDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscETitle.dtcETitleRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscETitle.dtcETitleRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscETitleTableAdapters.tacETitle">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscETitleTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscETitleTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscETitle,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscETitleTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscETitle,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscETitleTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscETitle,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscETitleTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscETitleTableAdapters.TableAdapterManager.UpdateAll(HR.dscETitle)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscETitleTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscETitleTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DW">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DW.dtAllowancesTrxDocDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DW.dtAllowancesTrxDocRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DW.dtAllowancesTrxDocRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DWTableAdapters.taAllowancesTrxDoc_DW">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsAllowancesTrxDoc_DW,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsAllowancesTrxDoc_DW,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsAllowancesTrxDoc_DW,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.UpdateAll(HR.dsAllowancesTrxDoc_DW)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsAllowancesTrxDoc_DWTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEAllowancesDoc">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAllowancesDoc.dtAllowancesTrxDocDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAllowancesDoc.dtAllowancesTrxDocRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAllowancesDoc.dtAllowancesTrxDocRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowancesDocTableAdapters.taAllowancesTrxDoc">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEAllowancesDocTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEAllowancesDoc,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEAllowancesDoc,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEAllowancesDoc,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.UpdateAll(HR.dsEAllowancesDoc)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEAllowancesDocTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEDocuments2_DW">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDocuments2_DW.dtEDocuments_DWDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDocuments2_DW.dtEDocuments_DWRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDocuments2_DW.dtEDocuments_DWRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDocuments2_DWTableAdapters.taEDocuments_DW">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEDocuments2_DW,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEDocuments2_DW,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEDocuments2_DW,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.UpdateAll(HR.dsEDocuments2_DW)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEDocuments2_DWTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter.dtEmpSupportLetterDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter.dtEmpSupportLetterQRCodeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter.dtEmpSupportLetterRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter.dtEmpSupportLetterQRCodeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter.dtEmpSupportLetterRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmpSupportLetter.dtEmpSupportLetterQRCodeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEmpDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEResponsibilityDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEChildrenDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEEmpTaxExemptionsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEProjectsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEmpRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEResponsibilityRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEChildrenRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEEmpTaxExemptionsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEProjectsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp2.dtEmpRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp2.dtEResponsibilityRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp2.dtEChildrenRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp2.dtEEmpTaxExemptionsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp2.dtEProjectsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.taEmp">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.taEResponsibility">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.taEChildren">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.taEEmpTaxExemptions">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.taEProjects">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEmp2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEmp2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEmp2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEmp2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEmp2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEmp2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEmp2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEmp2TableAdapters.TableAdapterManager.UpdateAll(HR.dsEmp2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEmp2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPayrollClosedBankPayrollLists">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPayrollClosedBankPayrollLists.dtEPayrollClosedBankPayrollListsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPayrollClosedBankPayrollLists.dtEPayrollClosedBankPayrollListsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPayrollClosedBankPayrollLists.dtEPayrollClosedBankPayrollListsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyIncentivesRep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyIncentivesRep.dtPayrollClosedMonthlyIncentivesRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyIncentivesRep.dtPayrollClosedMonthlyIncentivesRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyIncentivesRep.dtPayrollClosedMonthlyIncentivesRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsSocSecContributionsByFiles">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsSocSecContributionsByFiles.dtSocSecContributionsByFilesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsSocSecContributionsByFiles.dtSocSecContributionsByFilesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsSocSecContributionsByFiles.dtSocSecContributionsByFilesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep.dtEmpAllowancesDetailsRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep.dtEmpTotalSalaryDetailsRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep.dtEmpAllowancesDetailsRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep.dtEmpTotalSalaryDetailsRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep.dtEmpAllowancesDetailsRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmpAllowancesDetailsRep.dtEmpTotalSalaryDetailsRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPostedAllowancesRep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsPostedAllowancesRep.dtPostedAllowancesRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPostedAllowancesRep.dtPostedAllowancesRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPostedAllowancesRep.dtPostedAllowancesRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsGetEmploymentAgreementData">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsGetEmploymentAgreementData.dtGetEmploymentAgreementDataDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsGetEmploymentAgreementData.dtGetEmploymentAgreementDataRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsGetEmploymentAgreementData.dtGetEmploymentAgreementDataRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmpCards">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmpCards.dtEmpCardsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmpCards.dtEmpCardsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmpCards.dtEmpCardsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyTax_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyTax_Rep.dtPayrollClosedMonthlyTax_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyTax_Rep.dtPayrollClosedMonthlyTax_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPayrollClosedMonthlyTax_Rep.dtPayrollClosedMonthlyTax_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtDeptClosedMonthlyRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtSecurityClosedMonthlyRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtRentedVehiclesClosedMonthlyRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtDeptClosedMonthlyRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtSecurityClosedMonthlyRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtRentedVehiclesClosedMonthlyRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtDeptClosedMonthlyRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtSecurityClosedMonthlyRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsDeptClosedMonthly_Rep.dtRentedVehiclesClosedMonthlyRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRate">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRate.dtPeriodsExchangeRateDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRate.dtPeriodsExchangeRateRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRate.dtPeriodsExchangeRateRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRateTableAdapters.taPeriodsExchangeRate">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsPeriodsExchangeRate,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsPeriodsExchangeRate,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsPeriodsExchangeRate,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.UpdateAll(HR.dsPeriodsExchangeRate)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsPeriodsExchangeRateTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscESubSection">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscESubSection.dtcESubSectionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscESubSection.dtcESubSectionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscESubSection.dtcESubSectionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscESubSectionTableAdapters.tacESubSection">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscESubSectionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscESubSectionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscESubSection,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscESubSectionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscESubSection,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscESubSectionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscESubSection,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscESubSectionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscESubSectionTableAdapters.TableAdapterManager.UpdateAll(HR.dscESubSection)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscESubSectionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscESubSectionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscECostCentre">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscECostCentre.dtcECostCentreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscECostCentre.dtcECostCentreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscECostCentre.dtcECostCentreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscECostCentreTableAdapters.tacECostCentre">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscECostCentreTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscECostCentreTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscECostCentre,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscECostCentreTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscECostCentre,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscECostCentreTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscECostCentre,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscECostCentreTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscECostCentreTableAdapters.TableAdapterManager.UpdateAll(HR.dscECostCentre)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscECostCentreTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscECostCentreTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscECustodyCondition">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscECustodyCondition.dtcECustodyConditionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscECustodyCondition.dtcECustodyConditionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscECustodyCondition.dtcECustodyConditionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscECustodyConditionTableAdapters.tacECustodyCondition">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscECustodyConditionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscECustodyConditionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscECustodyCondition,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscECustodyConditionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscECustodyCondition,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscECustodyConditionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscECustodyCondition,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscECustodyConditionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscECustodyConditionTableAdapters.TableAdapterManager.UpdateAll(HR.dscECustodyCondition)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscECustodyConditionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscECustodyConditionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscECustodyType">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscECustodyType.dtcECustodyTypeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscECustodyType.dtcECustodyTypeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscECustodyType.dtcECustodyTypeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscECustodyTypeTableAdapters.tacECustodyType">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscECustodyTypeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscECustodyTypeTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscECustodyType,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscECustodyTypeTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscECustodyType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscECustodyTypeTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscECustodyType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscECustodyTypeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscECustodyTypeTableAdapters.TableAdapterManager.UpdateAll(HR.dscECustodyType)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscECustodyTypeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscECustodyTypeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEDailyAllowances">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEDailyAllowances.dtcEDailyAllowancesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEDailyAllowances.dtcEDailyAllowancesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEDailyAllowances.dtcEDailyAllowancesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEDailyAllowancesTableAdapters.tacEDailyAllowances">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEDailyAllowances,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEDailyAllowances,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEDailyAllowances,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.UpdateAll(HR.dscEDailyAllowances)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEDailyAllowancesTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEDepartmentSG">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEDepartmentSG.dtcEDepartmentSGDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEDepartmentSG.dtcEDepartmentSGRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEDepartmentSG.dtcEDepartmentSGRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEDepartmentSGTableAdapters.tacEDepartmentSG">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEDepartmentSGTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEDepartmentSG,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEDepartmentSG,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEDepartmentSG,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.UpdateAll(HR.dscEDepartmentSG)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEDepartmentSGTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEDepartment">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEDepartment.dtcEDepartmentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEDepartment.dtcEDepartmentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEDepartment.dtcEDepartmentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEDepartmentTableAdapters.tacEDepartment">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEDepartmentTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEDepartmentTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEDepartment,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDepartmentTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEDepartment,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDepartmentTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEDepartment,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEDepartmentTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEDepartmentTableAdapters.TableAdapterManager.UpdateAll(HR.dscEDepartment)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEDepartmentTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEDepartmentTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethod">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethod.dtcEDevelopmentMethodDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethod.dtcEDevelopmentMethodRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethod.dtcEDevelopmentMethodRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethodTableAdapters.tacEDevelopmentMethod">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEDevelopmentMethod,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEDevelopmentMethod,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEDevelopmentMethod,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.UpdateAll(HR.dscEDevelopmentMethod)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEDevelopmentMethodTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryAction">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryAction.dtcEDisciplinaryActionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryAction.dtcEDisciplinaryActionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryAction.dtcEDisciplinaryActionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryActionTableAdapters.tacEDisciplinaryAction">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsvuEDisciplinaryAction,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsvuEDisciplinaryAction,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsvuEDisciplinaryAction,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateAll(HR.dsvuEDisciplinaryAction)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsvuEDisciplinaryActionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEEmploymentType">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEEmploymentType.dtcEEmploymentTypeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEEmploymentType.dtcEEmploymentTypeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEEmploymentType.dtcEEmploymentTypeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEEmploymentTypeTableAdapters.tacEEmployment">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEEmploymentType,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEEmploymentType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEEmploymentType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.UpdateAll(HR.dscEEmploymentType)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEEmploymentTypeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEIndemnityRate">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEIndemnityRate.dtcEIndemnityRateDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEIndemnityRate.dtcEIndemnityRateRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEIndemnityRate.dtcEIndemnityRateRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEIndemnityRateTableAdapters.tacEIndemnityRate">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEIndemnityRateTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEIndemnityRate,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEIndemnityRate,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEIndemnityRate,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.UpdateAll(HR.dscEIndemnityRate)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEIndemnityRateTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEInjury_Reasons">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEInjury_Reasons.dtcEInjury_ReasonsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEInjury_Reasons.dtcEInjury_ReasonsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEInjury_Reasons.dtcEInjury_ReasonsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEInjury_ReasonsTableAdapters.tacEInjury_Reasons">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEInjury_Reasons,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEInjury_Reasons,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEInjury_Reasons,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.UpdateAll(HR.dscEInjury_Reasons)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEInjury_ReasonsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEInjury_Results">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEInjury_Results.dtcEInjury_ResultsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEInjury_Results.dtcEInjury_ResultsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEInjury_Results.dtcEInjury_ResultsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEInjury_ResultsTableAdapters.tacEInjury_Results">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEInjury_Results,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEInjury_Results,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEInjury_Results,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.UpdateAll(HR.dscEInjury_Results)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEInjury_ResultsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevels">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevels.dtcEInjury_RiskLevelsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevels.dtcEInjury_RiskLevelsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevels.dtcEInjury_RiskLevelsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevelsTableAdapters.tacEInjury_RiskLevels">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEInjury_RiskLevels,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEInjury_RiskLevels,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEInjury_RiskLevels,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.UpdateAll(HR.dscEInjury_RiskLevels)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEInjury_RiskLevelsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEInsuranceClasses">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEInsuranceClasses.dtcEInsuranceClassesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEInsuranceClasses.dtcEInsuranceClassesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEInsuranceClasses.dtcEInsuranceClassesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEInsuranceClassesTableAdapters.tacEInsuranceClasses">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEInsuranceClasses,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEInsuranceClasses,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEInsuranceClasses,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.UpdateAll(HR.dscEInsuranceClasses)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEInsuranceClassesTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEJobStatus">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEJobStatus.dtcJobStatusDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEJobStatus.dtcJobStatusRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEJobStatus.dtcJobStatusRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEJobStatusTableAdapters.tacJobStatus">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEJobStatusTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEJobStatusTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEJobStatus,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEJobStatusTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEJobStatus,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEJobStatusTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEJobStatus,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEJobStatusTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEJobStatusTableAdapters.TableAdapterManager.UpdateAll(HR.dscEJobStatus)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEJobStatusTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEJobStatusTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscELoanType">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscELoanType.dtcELoanTypeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscELoanType.dtcELoanTypeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscELoanType.dtcELoanTypeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscELoanTypeTableAdapters.dtcELoanType">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscELoanTypeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscELoanTypeTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscELoanType,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscELoanTypeTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscELoanType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscELoanTypeTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscELoanType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscELoanTypeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscELoanTypeTableAdapters.TableAdapterManager.UpdateAll(HR.dscELoanType)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscELoanTypeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscELoanTypeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEMaritalStatus">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEMaritalStatus.dtcEMaritalStatusDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEMaritalStatus.dtcEMaritalStatusRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEMaritalStatus.dtcEMaritalStatusRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEMaritalStatusTableAdapters.tacEMaritalStatus">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEMaritalStatusTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEMaritalStatus,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEMaritalStatus,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEMaritalStatus,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.UpdateAll(HR.dscEMaritalStatus)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEMaritalStatusTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscENationality">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscENationality.dtcENationalityDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscENationality.dtcENationalityRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscENationality.dtcENationalityRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscENationalityTableAdapters.tacENationality">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscENationalityTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscENationalityTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscENationality,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscENationalityTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscENationality,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscENationalityTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscENationality,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscENationalityTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscENationalityTableAdapters.TableAdapterManager.UpdateAll(HR.dscENationality)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscENationalityTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscENationalityTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEOTime_Type">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEOTime_Type.dtEOTime_TypeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEOTime_Type.dtEOTime_TypeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEOTime_Type.dtEOTime_TypeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEOTime_TypeTableAdapters.taEOTime_Type">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEOTime_TypeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEOTime_Type,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEOTime_Type,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEOTime_Type,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.UpdateAll(HR.dsEOTime_Type)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEOTime_TypeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEPreferredSource">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEPreferredSource.dtcEPreferredSourceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEPreferredSource.dtcEPreferredSourceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEPreferredSource.dtcEPreferredSourceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEPreferredSourceTableAdapters.tacEPreferredSource">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEPreferredSourceTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEPreferredSource,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEPreferredSource,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEPreferredSource,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.UpdateAll(HR.dscEPreferredSource)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEPreferredSourceTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEQualification">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEQualification.dtcEQualificationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEQualification.dtcEQualificationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEQualification.dtcEQualificationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEQualificationTableAdapters.tacEQualification">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEQualificationTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEQualificationTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEQualification,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEQualificationTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEQualification,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEQualificationTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEQualification,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEQualificationTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEQualificationTableAdapters.TableAdapterManager.UpdateAll(HR.dscEQualification)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEQualificationTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEQualificationTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEResignationReason">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEResignationReason.dtcEResignationReasonDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEResignationReason.dtcEResignationReasonRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEResignationReason.dtcEResignationReasonRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEResignationReasonTableAdapters.tacEResignationReason">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEResignationReasonTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEResignationReasonTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEResignationReason,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEResignationReasonTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEResignationReason,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEResignationReasonTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEResignationReason,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEResignationReasonTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEResignationReasonTableAdapters.TableAdapterManager.UpdateAll(HR.dscEResignationReason)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEResignationReasonTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEResignationReasonTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEResponsibility">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEResponsibility.dtcEResponsibilityDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEResponsibility.dtcEResponsibilityRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEResponsibility.dtcEResponsibilityRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEResponsibilityTableAdapters.tacEResponsibility">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEResponsibilityTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEResponsibilityTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEResponsibility,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEResponsibilityTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEResponsibility,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEResponsibilityTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEResponsibility,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEResponsibilityTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEResponsibilityTableAdapters.TableAdapterManager.UpdateAll(HR.dscEResponsibility)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEResponsibilityTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEResponsibilityTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscESection">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscESection.dtcESectionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscESection.dtcESectionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscESection.dtcESectionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscESectionTableAdapters.tacESection">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscESectionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscESectionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscESection,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscESectionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscESection,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscESectionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscESection,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscESectionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscESectionTableAdapters.TableAdapterManager.UpdateAll(HR.dscESection)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscESectionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscESectionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEVacancyType">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEVacancyType.dtcEVacancyTypeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEVacancyType.dtcEVacancyTypeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEVacancyType.dtcEVacancyTypeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEVacancyTypeTableAdapters.tacEVacancyType">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEVacancyTypeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEVacancyType,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEVacancyType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEVacancyType,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.UpdateAll(HR.dscEVacancyType)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEVacancyTypeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEWorkDone">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEWorkDone.dtcEWorkDoneDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEWorkDone.dtcEWorkDoneRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEWorkDone.dtcEWorkDoneRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEWorkDoneTableAdapters.tacEWorkDone">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEWorkDoneTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEWorkDoneTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEWorkDone,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEWorkDoneTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEWorkDone,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEWorkDoneTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEWorkDone,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEWorkDoneTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEWorkDoneTableAdapters.TableAdapterManager.UpdateAll(HR.dscEWorkDone)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEWorkDoneTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEWorkDoneTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dscEWorkforceSector">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dscEWorkforceSector.dtcEWorkforceSectorDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dscEWorkforceSector.dtcEWorkforceSectorRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dscEWorkforceSector.dtcEWorkforceSectorRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dscEWorkforceSectorTableAdapters.tacEWorkforceSector">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dscEWorkforceSector,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dscEWorkforceSector,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dscEWorkforceSector,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.UpdateAll(HR.dscEWorkforceSector)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dscEWorkforceSectorTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEAbsence_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAbsence_Rep.dtEAbsence_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAbsence_Rep.dtEAbsence_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAbsence_Rep.dtEAbsence_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAccidentBenchmarking_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAccidentBenchmarking_Rep.dtEAccidentBenchMarking_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAccidentBenchmarking_Rep.dtEAccidentBenchMarking_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAccidentBenchmarking_Rep.dtEAccidentBenchMarking_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2.dtEAllowancesTrxDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2.dtEAllowancesTrxRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2.dtEAllowancesTrxRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2TableAdapters.taEAllowancesTrx">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEAllowancesTrx2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEAllowancesTrx2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEAllowancesTrx2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.UpdateAll(HR.dsEAllowancesTrx2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEAllowancesChanges_Lst">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAllowancesChanges_Lst.dtEAllowancesChanges_LstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAllowancesChanges_Lst.dtEAllowancesChanges_LstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAllowancesChanges_Lst.dtEAllowancesChanges_LstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx_Log.dtEAllowancesTrx_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx_Log.dtEAllowancesTrx_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAllowancesTrx_Log.dtEAllowancesTrx_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition.dtEAllowancesTypes_AllowancesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition.dtEAllowancesTypes_DeductionsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition.dtEAllowancesTypes_AllowancesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition.dtEAllowancesTypes_DeductionsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition.dtEAllowancesTypes_AllowancesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowances_Definition.dtEAllowancesTypes_DeductionsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAllowances_DefinitionTableAdapters.taEAllowancesTypes_Allowances">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEAllowances_DefinitionTableAdapters.taEAllowancesTypes_Deductions">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEAllowances_Definition,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEAllowances_Definition,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEAllowances_Definition,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.UpdateAll(HR.dsEAllowances_Definition)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEAllowances_DefinitionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan.dtEAnnualTrainingPlanDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan.dtEAnnualTrainingPlanParticipantsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan.dtEAnnualTrainingPlanRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan.dtEAnnualTrainingPlanParticipantsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan.dtEAnnualTrainingPlanRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan.dtEAnnualTrainingPlanParticipantsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlanTableAdapters.taEAnnualTrainingPlan">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlanTableAdapters.taEAnnualTrainingPlanParticipants">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEAnnualTrainingPlan,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEAnnualTrainingPlan,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEAnnualTrainingPlan,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.UpdateAll(HR.dsEAnnualTrainingPlan)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlanTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Log.dtEAnnualTrainingPlan_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Log.dtEAnnualTrainingPlan_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Log.dtEAnnualTrainingPlan_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Print.dtEAnnualTrainingPlan_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Print.dtEAnnualTrainingPlan_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEAnnualTrainingPlan_Print.dtEAnnualTrainingPlan_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEJobApplication_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEJobApplication_Log.dtEJobApplication_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEJobApplication_Log.dtEJobApplication_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEJobApplication_Log.dtEJobApplication_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_Report">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmp_Report.dtEmp_ReportDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_Report.dtPayrollDeptAuditDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_Report.dtEmp_ReportRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_Report.dtPayrollDeptAuditRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_Report.dtEmp_ReportRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_Report.dtPayrollDeptAuditRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPayrollBankRep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPayrollBankRep.dtEPayrollBankRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPayrollBankRep.dtEPayrollBankRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPayrollBankRep.dtEPayrollBankRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.InsightHRDataSet">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.InsightHRDataSet.EmpDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.InsightHRDataSet.EmpRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.InsightHRDataSet.EmpRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.InsightHRDataSetTableAdapters.EmpTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.InsightHRDataSetTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.InsightHRDataSetTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.InsightHRDataSet,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.InsightHRDataSetTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.InsightHRDataSet,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.InsightHRDataSetTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.InsightHRDataSet,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.InsightHRDataSetTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.InsightHRDataSetTableAdapters.TableAdapterManager.UpdateAll(HR.InsightHRDataSet)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.InsightHRDataSetTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.InsightHRDataSetTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsHRSGIntegration">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsHRSGIntegration.dtESageJournalBatchDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsHRSGIntegration.dtESageJournalBatchRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsHRSGIntegration.dtESageJournalBatchRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPayrollPayment2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2.dtPayrollPaymentDetailsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2.dtPayrollPaymentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2.dtPayrollPaymentDetailsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2.dtPayrollPaymentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2.dtPayrollPaymentDetailsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPayrollPayment2.dtPayrollPaymentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsPayrollPayment2TableAdapters.taPayrollPaymentDetails">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2TableAdapters.taPayrollPayment">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsPayrollPayment2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsPayrollPayment2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsPayrollPayment2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.UpdateAll(HR.dsPayrollPayment2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsPayrollPayment2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsPayrollPaymentRep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsPayrollPaymentRep.dtPayrollPaymenDetailsRepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsPayrollPaymentRep.dtPayrollPaymenDetailsRepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsPayrollPaymentRep.dtPayrollPaymenDetailsRepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsAttendance_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsAttendance_Rep.dtAttendance_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsAttendance_Rep.dtAttendance_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsAttendance_Rep.dtAttendance_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsECustodyStatus_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsECustodyStatus_Rep.dtECustodyStatus_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsECustodyStatus_Rep.dtECustodyStatus_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsECustodyStatus_Rep.dtECustodyStatus_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsECustody">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsECustody.dtECustodyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsECustody.dtECustodyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsECustody.dtECustodyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsECustodyTableAdapters.taECustody">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsECustodyTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsECustodyTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsECustody,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsECustodyTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsECustody,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsECustodyTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsECustody,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsECustodyTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsECustodyTableAdapters.TableAdapterManager.UpdateAll(HR.dsECustody)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsECustodyTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsECustodyTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsECustody_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsECustody_Log.dtECustody_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsECustody_Log.dtECustody_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsECustody_Log.dtECustody_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsECustody_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsECustody_Print.dtECustody_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsECustody_Print.dtECustody_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsECustody_Print.dtECustody_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep.dtEDailyAllowance_Rep_SumDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep.dtEDailyAllowance_Rep_Details_SumDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep.dtEDailyAllowance_Rep_SumRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep.dtEDailyAllowance_Rep_Details_SumRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep.dtEDailyAllowance_Rep_SumRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDailyAllowance_Rep.dtEDailyAllowance_Rep_Details_SumRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDepartmentTrainingStatus_Rep_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDepartmentTrainingStatus_Rep_Print.dtEDepartmentTrainingStatus_Rep_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDepartmentTrainingStatus_Rep_Print.dtEDepartmentTrainingStatus_Rep_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDepartmentTrainingStatus_Rep_Print.dtEDepartmentTrainingStatus_Rep_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan.dtEDepartmentTrainingPlanDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan.dtEDepartmentTrainingPlanRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan.dtEDepartmentTrainingPlanRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlanTableAdapters.taEDepartmentTrainingPlan">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEDeptartmentTrainingPlan,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEDeptartmentTrainingPlan,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEDeptartmentTrainingPlan,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.UpdateAll(HR.dsEDeptartmentTrainingPlan)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlanTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan_Print.dtEDepartmentTrainingPlan_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan_Print.dtEDepartmentTrainingPlan_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDeptartmentTrainingPlan_Print.dtEDepartmentTrainingPlan_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction.dtEDisciplinaryActionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction.dtEDisciplinaryActionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction.dtEDisciplinaryActionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDisciplinaryActionTableAdapters.taEDisciplinaryAction">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEDisciplinaryAction,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEDisciplinaryAction,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEDisciplinaryAction,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateAll(HR.dsEDisciplinaryAction)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEDisciplinaryActionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Log.dtEDisciplinaryAction_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Log.dtEDisciplinaryAction_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Log.dtEDisciplinaryAction_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Print.dtEDisciplinaryAction_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Print.dtEDisciplinaryAction_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDisciplinaryAction_Print.dtEDisciplinaryAction_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkforceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtDeptDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkForceAndDept_SumDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkForceAndDept_DetailsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkforceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtDeptRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkForceAndDept_SumRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkForceAndDept_DetailsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkforceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtDeptRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkForceAndDept_SumRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDistOfEmp_Rep.dtWorkForceAndDept_DetailsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDocuments2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEDocuments2.dtEDocumentsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEDocuments2.dtEDocumentsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEDocuments2.dtEDocumentsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEDocuments2TableAdapters.taEDocuments">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEDocuments2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEDocuments2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEDocuments2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDocuments2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEDocuments2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEDocuments2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEDocuments2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEDocuments2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEDocuments2TableAdapters.TableAdapterManager.UpdateAll(HR.dsEDocuments2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEDocuments2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEDocuments2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEEmpAge_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpAge_Rep.dtEEmpAge_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAge_Rep.dtEEmpAge_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAge_Rep.dtEEmpAge_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_CurrentEmployeesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedAfterDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedBetweenDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForLessThanDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_ResignedBetweenDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_MOWDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForMoreThanDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_CurrentEmployeesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedAfterRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedBetweenRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForLessThanRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_ResignedBetweenRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_MOWRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForMoreThanRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_CurrentEmployeesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedAfterRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedBetweenRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForLessThanRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_ResignedBetweenRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_MOWRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpAppointment_Rep.dtEEmpAppointment_Rep_EmployedForMoreThanRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpExperienceYears_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpExperienceYears_Rep.dtEEmpExperienceYears_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpExperienceYears_Rep.dtEEmpExperienceYears_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpExperienceYears_Rep.dtEEmpExperienceYears_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpRequisition">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition.dtEEmpRequisitionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition.dtEEmpRequisitionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition.dtEEmpRequisitionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpRequisitionTableAdapters.taEEmpRequisition">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEEmpRequisition,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEEmpRequisition,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEEmpRequisition,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.UpdateAll(HR.dsEEmpRequisition)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEEmpRequisitionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Log.dtEEmpRequisition_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Log.dtEEmpRequisition_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Log.dtEEmpRequisition_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Print.dtEEmpRequisition_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Print.dtEEmpRequisition_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpRequisition_Print.dtEEmpRequisition_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEIndemnity_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEIndemnity_Rep.dtEIndemnity_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEIndemnity_Rep.dtEIndemnity_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEIndemnity_Rep.dtEIndemnity_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockData">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockData.dtEInOut_GetClockDataDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockData.dtEInOut_GetClockDataRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockData.dtEInOut_GetClockDataRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockDataTableAdapters.taEInOut_GetClockData">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEInOut_GetClockData,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEInOut_GetClockData,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEInOut_GetClockData,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.UpdateAll(HR.dsEInOut_GetClockData)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEInOut_GetClockDataTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEHiring_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtSummaryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtHiredDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtResignedDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtSummaryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtHiredRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtResignedRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtSummaryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtHiredRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEHiring_Rep.dtResignedRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjuryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjury_RestDaysDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjury_ExpensesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjuryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjury_RestDaysRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjury_ExpensesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjuryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjury_RestDaysRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury2.dtEInjury_ExpensesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury2TableAdapters.taEInjury_Expenses">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEInjury2TableAdapters.taEInjury">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEInjury2TableAdapters.taEInjury_RestDays">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEInjury2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEInjury2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEInjury2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEInjury2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEInjury2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEInjury2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEInjury2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEInjury2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEInjury2TableAdapters.TableAdapterManager.UpdateAll(HR.dsEInjury2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEInjury2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEInjury2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEInjury_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_Log_ExpensesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_Log_RestDaysDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_Log_ExpensesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_Log_RestDaysRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_Log_ExpensesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury_Log.dtEInjury_Log_RestDaysRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEJobApplication">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEJobApplication.dtEJobApplicationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEJobApplication.dtEJobApplicationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEJobApplication.dtEJobApplicationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEJobApplicationTableAdapters.taEJobApplication">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEJobApplicationTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEJobApplicationTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEJobApplication,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEJobApplicationTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEJobApplication,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEJobApplicationTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEJobApplication,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEJobApplicationTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEJobApplicationTableAdapters.TableAdapterManager.UpdateAll(HR.dsEJobApplication)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEJobApplicationTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEJobApplicationTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEJobApplication_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEJobApplication_Print.dtEJobApplication_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEJobApplication_Print.dtEJobApplication_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEJobApplication_Print.dtEJobApplication_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEJobs">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEJobs.dtEJobsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEJobs.dtEJobsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEJobs.dtEJobsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEJobsTableAdapters.taEJobs">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEJobsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEJobsTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEJobs,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEJobsTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEJobs,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEJobsTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEJobs,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEJobsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEJobsTableAdapters.TableAdapterManager.UpdateAll(HR.dsEJobs)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEJobsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEJobsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEJobs_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEJobs_Log.dtEJobs_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEJobs_Log.dtEJobs_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEJobs_Log.dtEJobs_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEJobs_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEJobs_Print.dtEJobs_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEJobs_Print.dtEJobs_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEJobs_Print.dtEJobs_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaveRequest">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaveRequest.dtELeaveRequestDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveRequest.dtELeaveRequestRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveRequest.dtELeaveRequestRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaveRequestTableAdapters.taELeaveRequest">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsELeaveRequestTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsELeaveRequestTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsELeaveRequest,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsELeaveRequestTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsELeaveRequest,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsELeaveRequestTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsELeaveRequest,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsELeaveRequestTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsELeaveRequestTableAdapters.TableAdapterManager.UpdateAll(HR.dsELeaveRequest)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsELeaveRequestTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsELeaveRequestTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Log.dtELeaveRequest_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Log.dtELeaveRequest_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Log.dtELeaveRequest_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Print.dtELeaveRequest_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Print.dtELeaveRequest_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveRequest_Print.dtELeaveRequest_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep.dtSummaryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep.dtGraphDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep.dtSummaryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep.dtGraphRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep.dtSummaryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaveMonthlySum_Rep.dtGraphRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaveSum_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaveSum_Rep.dtELeaveSum_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveSum_Rep.dtELeaveSum_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveSum_Rep.dtELeaveSum_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaves2.dtELeavesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves2.dtELeavesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves2.dtELeavesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_JobInjuryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_RegDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_SickDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_UnPaidDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_AllDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_SummaryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_JobInjuryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_RegRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_SickRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_UnPaidRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_AllRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_SummaryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_JobInjuryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_RegRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_SickRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_UnPaidRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_AllRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves_Rep.dtELeave_Rep_SummaryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEloans">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEloans.dtELoansDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEloans.dtELoansTrxDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEloans.dtELoansRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEloans.dtELoansTrxRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEloans.dtELoansRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEloans.dtELoansTrxRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEloansTableAdapters.taELoans">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEloansTableAdapters.taELoansTrx">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEloansTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEloansTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEloans,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEloansTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEloans,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEloansTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEloans,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEloansTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEloansTableAdapters.TableAdapterManager.UpdateAll(HR.dsEloans)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEloansTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEloansTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEloans_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEloans_Log.dtELoans_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEloans_Log.dtELoans_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEloans_Log.dtELoans_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEloans_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEloans_Print.dtELoans_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEloans_Print.dtELoans_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEloans_Print.dtELoans_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_BankDetailsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_PaymentsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_BankSummaryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_BankDetailsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_PaymentsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_BankSummaryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_BankDetailsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_PaymentsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEMonthlyDueSalaries_Rep.dtEPay_Rep_BankSummaryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtEmpSummary_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtELeaveSummary_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtEOTimeSummary_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtETimeSheetSummary_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtEmpSummary_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtELeaveSummary_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtEOTimeSummary_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtETimeSheetSummary_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtEmpSummary_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtELeaveSummary_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtEOTimeSummary_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpSummary_Rep.dtETimeSheetSummary_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEEmpSummary_RepTableAdapters.taEmpSummary_Rep">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEEmpWorkHours_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEEmpWorkHours_Rep.dtEEmpWorkHours_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEEmpWorkHours_Rep.dtEEmpWorkHours_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEEmpWorkHours_Rep.dtEEmpWorkHours_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEInjury_Lst">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEInjury_Lst.dtEInjury_LstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEInjury_Lst.dtEInjury_LstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEInjury_Lst.dtEInjury_LstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsELeaves2TableAdapters.taELeave">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsELeaves2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsELeaves2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsELeaves2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsELeaves2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsELeaves2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsELeaves2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsELeaves2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsELeaves2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsELeaves2TableAdapters.TableAdapterManager.UpdateAll(HR.dsELeaves2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsELeaves2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsELeaves2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsELeaveBalances_Lst">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsELeaveBalances_Lst.dtELeaveBalances_LstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsELeaveBalances_Lst.dtELeaveBalances_LstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsELeaveBalances_Lst.dtELeaveBalances_LstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmpAllowances_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmpAllowances_Rep.dtEmpAllowances_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmpAllowances_Rep.dtEmpAllowances_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmpAllowances_Rep.dtEmpAllowances_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEmpDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEChildrenDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEResponsibilityDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEmpRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEChildrenRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEResponsibilityRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEmpRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEChildrenRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_Log.dtEResponsibilityRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_Lst_InsCont">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmp_Lst_InsCont.dtEmp_Lst_InsContDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_Lst_InsCont.dtEmp_Lst_InsContRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_Lst_InsCont.dtEmp_Lst_InsContRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEmp_AvlLeaves_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEmp_AvlLeaves_Rep.dtEmp_AvlLeavesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEmp_AvlLeaves_Rep.dtEmp_AvlLeavesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEmp_AvlLeaves_Rep.dtEmp_AvlLeavesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEOTime2">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEOTime2.dtEOTimeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEOTime2.dtEOTimeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEOTime2.dtEOTimeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEOTime2TableAdapters.taEOTime">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEOTime2TableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEOTime2TableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEOTime2,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEOTime2TableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEOTime2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEOTime2TableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEOTime2,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEOTime2TableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEOTime2TableAdapters.TableAdapterManager.UpdateAll(HR.dsEOTime2)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEOTime2TableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEOTime2TableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEOTime_Lst">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEOTime_Lst.dtEOTime_LstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEOTime_Lst.dtEOTime_LstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEOTime_Lst.dtEOTime_LstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep.dtEPayrollClosed_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep.dtEPayrollClosed_Lst_SocSecDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep.dtEPayrollClosed_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep.dtEPayrollClosed_Lst_SocSecRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep.dtEPayrollClosed_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPayrollClosed_Rep.dtEPayrollClosed_Lst_SocSecRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep.dtEPayroll_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep.dtEPayroll_Lst_SocSecDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep.dtEPayroll_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep.dtEPayroll_Lst_SocSecRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep.dtEPayroll_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPayroll_Rep.dtEPayroll_Lst_SocSecRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESocSec_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtESocSecDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtSocSec_TaxStatementsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtSocSec_General_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtESocSecRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtSocSec_TaxStatementsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtSocSec_General_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtESocSecRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtSocSec_TaxStatementsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESocSec_Rep.dtSocSec_General_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsWorkPermitRemainingDays_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsWorkPermitRemainingDays_Rep.dtWorkPermitRemainingDays_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsWorkPermitRemainingDays_Rep.dtWorkPermitRemainingDays_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsWorkPermitRemainingDays_Rep.dtWorkPermitRemainingDays_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPay_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_PaymentsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_BankDetailsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_BankSummaryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_PaymentsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_BankDetailsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_BankSummaryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_PaymentsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_BankDetailsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPay_Rep.dtEPay_Rep_BankSummaryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPay_RepTableAdapters.taEPay_Rep_Payments">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPay_RepTableAdapters.taEPay_Rep_BankDetails">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp.dtEPerformanceEval_EmpDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp.dtEPerformanceEval_EmpRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp.dtEPerformanceEval_EmpRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_EmpTableAdapters.taEPerformanceEval_Emp">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPerformanceEval_Emp,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPerformanceEval_Emp,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPerformanceEval_Emp,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPerformanceEval_Emp)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_EmpTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Log.dtEPerformanceEval_Emp_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Log.dtEPerformanceEval_Emp_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Log.dtEPerformanceEval_Emp_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Print.dtEPerformanceEval_Emp_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Print.dtEPerformanceEval_Emp_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Emp_Print.dtEPerformanceEval_Emp_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor.dtEPerformanceEval_SupervisorDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor.dtEPerformanceEval_SupervisorRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor.dtEPerformanceEval_SupervisorRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_SupervisorTableAdapters.taEPerformanceEval_Supervisor">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPerformanceEval_Supervisor,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPerformanceEval_Supervisor,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPerformanceEval_Supervisor,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPerformanceEval_Supervisor)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_SupervisorTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Log.dtEPerformanceEval_Supervisor_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Log.dtEPerformanceEval_Supervisor_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Log.dtEPerformanceEval_Supervisor_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Print.dtEPerformanceEval_Supervisor_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Print.dtEPerformanceEval_Supervisor_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Supervisor_Print.dtEPerformanceEval_Supervisor_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician.dtEPerformanceEval_TechnicianDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician.dtEPerformanceEval_TechnicianRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician.dtEPerformanceEval_TechnicianRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_TechnicianTableAdapters.taEPerformanceEval_Technician">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPerformanceEval_Technician,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPerformanceEval_Technician,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPerformanceEval_Technician,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPerformanceEval_Technician)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_TechnicianTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Log.dtEPerformanceEval_Technician_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Log.dtEPerformanceEval_Technician_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Log.dtEPerformanceEval_Technician_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Print.dtEPerformanceEval_Technician_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Print.dtEPerformanceEval_Technician_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceEval_Technician_Print.dtEPerformanceEval_Technician_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring.dtEPerformanceMonitoringObjectivesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring.dtEPerformanceMonitoringDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring.dtEPerformanceMonitoringObjectivesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring.dtEPerformanceMonitoringRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring.dtEPerformanceMonitoringObjectivesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring.dtEPerformanceMonitoringRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoringTableAdapters.taEPerformanceMonitoringObjectives">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoringTableAdapters.taEPerformanceMonitoring">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPerformanceMonitoring,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPerformanceMonitoring,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPerformanceMonitoring,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPerformanceMonitoring)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoringTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Log.dtEPerformanceMonitoring_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Log.dtEPerformanceMonitoring_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Log.dtEPerformanceMonitoring_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Print.dtEPerformanceMonitoring_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Print.dtEPerformanceMonitoring_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceMonitoring_Print.dtEPerformanceMonitoring_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord.dtEPerformanceRecordDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord.dtEPerformanceRecordOutcomeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord.dtEPerformanceRecordRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord.dtEPerformanceRecordOutcomeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord.dtEPerformanceRecordRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord.dtEPerformanceRecordOutcomeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceRecordTableAdapters.taEPerformanceRecord">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecordTableAdapters.taEPerformanceRecordOutcome">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPerformanceRecord,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPerformanceRecord,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPerformanceRecord,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPerformanceRecord)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPerformanceRecordTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Log.dtEPerformanceRecord_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Log.dtEPerformanceRecord_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Log.dtEPerformanceRecord_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Print.dtEPerformanceRecord_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Print.dtEPerformanceRecord_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPerformanceRecord_Print.dtEPerformanceRecord_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPictures">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPictures.dtEPicturesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPictures.dtEPicturesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPictures.dtEPicturesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPicturesTableAdapters.taEPictures">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPicturesTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPicturesTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPictures,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPicturesTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPictures,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPicturesTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPictures,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPicturesTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPicturesTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPictures)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPicturesTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPicturesTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation.dtEProbationPeriodEvaluationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation.dtEProbationPeriodEvaluationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation.dtEProbationPeriodEvaluationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluationTableAdapters.taEProbationPeriodEvaluation">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEProbationPeriodEvaluation,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEProbationPeriodEvaluation,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEProbationPeriodEvaluation,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.UpdateAll(HR.dsEProbationPeriodEvaluation)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluationTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Log.dtEProbationPeriodEvaluation_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Log.dtEProbationPeriodEvaluation_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Log.dtEProbationPeriodEvaluation_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Print.dtEProbationPeriodEvaluation_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Print.dtEProbationPeriodEvaluation_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEProbationPeriodEvaluation_Print.dtEProbationPeriodEvaluation_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPromotion">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPromotion.dtEPromotionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPromotion.dtEPromotionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPromotion.dtEPromotionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPromotionTableAdapters.taEPromotion">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEPromotionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEPromotionTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEPromotion,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPromotionTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEPromotion,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEPromotionTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEPromotion,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEPromotionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEPromotionTableAdapters.TableAdapterManager.UpdateAll(HR.dsEPromotion)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEPromotionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEPromotionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEPromotion_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPromotion_Log.dtEPromotion_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPromotion_Log.dtEPromotion_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPromotion_Log.dtEPromotion_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEPromotion_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEPromotion_Print.dtEPromotion_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEPromotion_Print.dtEPromotion_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEPromotion_Print.dtEPromotion_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESignature">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESignature.dtESignatureDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESignature.dtESignatureRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESignature.dtESignatureRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESignatureTableAdapters.taESignature">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsESignatureTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsESignatureTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsESignature,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsESignatureTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsESignature,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsESignatureTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsESignature,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsESignatureTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsESignatureTableAdapters.TableAdapterManager.UpdateAll(HR.dsESignature)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsESignatureTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsESignatureTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsESignature_Lst">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESignature_Lst.dtESignatureDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESignature_Lst.dtESignatureRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESignature_Lst.dtESignatureRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep.dtAgeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep.dtYearsOfServiceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep.dtAgeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep.dtYearsOfServiceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep.dtAgeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEStatistics_Rep.dtYearsOfServiceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEStatistics_RepTableAdapters.dtAge">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEStatistics_RepTableAdapters.dtYearsOfService">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEStatistics_RepTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEStatistics_Rep,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEStatistics_Rep,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEStatistics_Rep,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.UpdateAll(HR.dsEStatistics_Rep)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEStatistics_RepTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning.dtESuccessionPlanningDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning.dtESuccessionPlanningRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning.dtESuccessionPlanningRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESuccessionPlanningTableAdapters.taESuccessionPlanning">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsESuccessionPlanning,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsESuccessionPlanning,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsESuccessionPlanning,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.UpdateAll(HR.dsESuccessionPlanning)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsESuccessionPlanningTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Log.dtESuccessionPlanning_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Log.dtESuccessionPlanning_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Log.dtESuccessionPlanning_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Print.dtESuccessionPlanning_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Print.dtESuccessionPlanning_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESuccessionPlanning_Print.dtESuccessionPlanning_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESuggestions">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESuggestions.dtESuggestionsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESuggestions.dtESuggestionsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESuggestions.dtESuggestionsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESuggestionsTableAdapters.dtESuggestions">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsESuggestionsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsESuggestionsTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsESuggestions,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsESuggestionsTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsESuggestions,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsESuggestionsTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsESuggestions,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsESuggestionsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsESuggestionsTableAdapters.TableAdapterManager.UpdateAll(HR.dsESuggestions)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsESuggestionsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsESuggestionsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsESuggesstions_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESuggesstions_Log.dtESuggestions_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESuggesstions_Log.dtESuggestions_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESuggesstions_Log.dtESuggestions_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsESuggesstions_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsESuggesstions_Print.dtESuggestions_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsESuggesstions_Print.dtESuggestions_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsESuggesstions_Print.dtESuggestions_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETimesheet">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETimesheet.dtETimeSheetDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETimesheet.dtETimeSheetRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETimesheet.dtETimeSheetRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETimesheetTableAdapters.taETimeSheet">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsETimesheetTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsETimesheetTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsETimesheet,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETimesheetTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsETimesheet,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETimesheetTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsETimesheet,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsETimesheetTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsETimesheetTableAdapters.TableAdapterManager.UpdateAll(HR.dsETimesheet)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsETimesheetTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsETimesheetTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsETimesheet_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETimesheet_Log.dtETimeSheet_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETimesheet_Log.dtETimeSheet_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETimesheet_Log.dtETimeSheet_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETotalMonthlyDueSalaries_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETotalMonthlyDueSalaries_Rep.dtETotalMonthlyDueSalaries_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETotalMonthlyDueSalaries_Rep.dtETotalMonthlyDueSalaries_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETotalMonthlyDueSalaries_Rep.dtETotalMonthlyDueSalaries_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance.dtETrainingCourseAttendanceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance.dtETrainingCourseAttendanceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance.dtETrainingCourseAttendanceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendanceTableAdapters.taETrainingCourseAttendance">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsETrainingCourseAttendance,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsETrainingCourseAttendance,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsETrainingCourseAttendance,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.UpdateAll(HR.dsETrainingCourseAttendance)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendanceTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Log.dtETrainingCourseAttendanceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Log.dtETrainingCourseAttendanceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Log.dtETrainingCourseAttendanceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Print.dtETrainingCourseAttendance_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Print.dtETrainingCourseAttendance_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETrainingCourseAttendance_Print.dtETrainingCourseAttendance_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment.dtETrainingNeedsAssessmentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment.dtETrainingNeedsAssessmentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment.dtETrainingNeedsAssessmentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestmentTableAdapters.taETrainingNeedsAssessment">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsETrainingNeedsAssestment,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsETrainingNeedsAssestment,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsETrainingNeedsAssestment,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.UpdateAll(HR.dsETrainingNeedsAssestment)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestmentTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment_Print.dtETrainingNeedsAssessment_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment_Print.dtETrainingNeedsAssessment_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETrainingNeedsAssestment_Print.dtETrainingNeedsAssessment_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingNeeds_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETrainingNeeds_Rep.dtETrainingNeeds_RepDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETrainingNeeds_Rep.dtETrainingNeeds_RepRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETrainingNeeds_Rep.dtETrainingNeeds_RepRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETraining">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETraining.dtETrainingDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETraining.dtETrainingRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETraining.dtETrainingRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETrainingTableAdapters.taETraining">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsETrainingTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsETrainingTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsETraining,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETrainingTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsETraining,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsETrainingTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsETraining,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsETrainingTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsETrainingTableAdapters.TableAdapterManager.UpdateAll(HR.dsETraining)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsETrainingTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsETrainingTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsETraining_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETraining_Log.dtETraining_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETraining_Log.dtETraining_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETraining_Log.dtETraining_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETraining_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETraining_Print.dtETraining_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETraining_Print.dtETraining_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETraining_Print.dtETraining_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETransportation_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsETransportation_Rep.dtETransportation_Rep_SumDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETransportation_Rep.dtETransportation_Rep_DetailsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsETransportation_Rep.dtETransportation_Rep_SumRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETransportation_Rep.dtETransportation_Rep_DetailsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsETransportation_Rep.dtETransportation_Rep_SumRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETransportation_Rep.dtETransportation_Rep_DetailsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsETransportation_RepTableAdapters.taETransportation_Rep_Sum">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsETransportation_RepTableAdapters.taETransportation_Rep_Details">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEleave_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEleave_Log.dtELeave_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEleave_Log.dtELeave_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEleave_Log.dtELeave_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEOTime_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEOTime_Log.dtEOTime_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEOTime_Log.dtEOTime_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEOTime_Log.dtEOTime_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEVacationRequest">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEVacationRequest.dtEVacationRequestDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEVacationRequest.dtEVacationRequestRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEVacationRequest.dtEVacationRequestRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEVacationRequestTableAdapters.taEVacationRequest">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:HR.dsEVacationRequestTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:HR.dsEVacationRequestTableAdapters.TableAdapterManager.UpdateUpdatedRows(HR.dsEVacationRequest,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEVacationRequestTableAdapters.TableAdapterManager.UpdateInsertedRows(HR.dsEVacationRequest,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:HR.dsEVacationRequestTableAdapters.TableAdapterManager.UpdateDeletedRows(HR.dsEVacationRequest,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:HR.dsEVacationRequestTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:HR.dsEVacationRequestTableAdapters.TableAdapterManager.UpdateAll(HR.dsEVacationRequest)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:HR.dsEVacationRequestTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:HR.dsEVacationRequestTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Log">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Log.dtEVacationRequest_LogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Log.dtEVacationRequest_LogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Log.dtEVacationRequest_LogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Print">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Print.dtEVacationRequest_PrintDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Print.dtEVacationRequest_PrintRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEVacationRequest_Print.dtEVacationRequest_PrintRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_DetailsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_SummaryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_TotalDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_Details_MonthlyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_TaxDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_DetailsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_SummaryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_TotalRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_Details_MonthlyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_TaxRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_DetailsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_SummaryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_TotalRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_Details_MonthlyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEYearSummary_Rep.dtEYearSummary_Rep_TaxRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:HR.dsEYearSummary_Lst_Details_Monthly">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Lst_Details_Monthly.dtEYearSummary_Lst_Details_MonthlyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Lst_Details_Monthly.dtEYearSummary_Lst_Details_MonthlyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:HR.dsEYearSummary_Lst_Details_Monthly.dtEYearSummary_Lst_Details_MonthlyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
</members>
</doc>
